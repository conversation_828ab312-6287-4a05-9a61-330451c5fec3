import io
import time

import requests
import streamlit as st  # type: ignore
import cv2
import os
import logging

import numpy as np
from PIL import Image


FLASK_API_BASE_URL = "http://localhost:5000"
START_TRANSACTION_ENDPOINT = f"{FLASK_API_BASE_URL}/start_transaction"
PROCESS_FRAME_ENDPOINT = F"{FLASK_API_BASE_URL}/process_frame"
STOP_TRANSACTION_ENDPOINT = f"{FLASK_API_BASE_URL}/stop_transaction"
# this needs to match alias used in Flask for serving output files
FLASK_SERVED_OUTPUT_ALIAS = "outputs"

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("__name__")

st.set_page_config(layout="wide", page_title="Live Face Detection via API")
st.title("Live Face Detection")
st.markdown("""Enter an RTSP URL to start live video feed. Frames will be sent to the backend API
                for face detection. Detected faces and age categories will be displayed.""")

# Session state
if "active_transaction_id_ui" not in st.session_state:
    st.session_state.active_transaction_id_ui = None
if "camera_source_input" not in st.session_state:
    st.session_state.camera_source_input = "0"
if "video_capture_object" not in st.session_state:
    st.session_state.video_capture_object = None
if "last_processed_annotated_image" not in st.session_state:  # To display result from API
    st.session_state.last_processed_annotated_image = None
if "last_detection_details_from_api" not in st.session_state:
    st.session_state.last_detection_details_from_api = None

# Display logic
if "display_mode" not in st.session_state:
    st.session_state.display_mode = "live_feed"
if "result_annotated_image" not in st.session_state:  # Stores the image from API response
    st.session_state.result_annotated_image = None
if "result_detection_details" not in st.session_state:  # Stores detection JSON from API
    st.session_state.result_detection_details = None


def call_start_transaction_api(transaction_id):
    payload = {"transaction_id": transaction_id}
    try:
        response = requests.post(START_TRANSACTION_ENDPOINT, json=payload, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"API Error (start_transaction): {e}")
        return {"error": str(e)}


def call_stop_transaction_api(transaction_id):
    payload = {"transaction_id": transaction_id}
    try:
        response = requests.post(STOP_TRANSACTION_ENDPOINT, json=payload, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"Api Error (stop_transaction): {e}")
        return {"error": str(e)}


def call_process_frame_api(frame_bgr_numpy):  # raw cv2 frame
    """
    Sends a frame to the Flask API and gets results.
    Returns the annotated image (fetched from the API), list of face crop images,
    and detection details.
    """
    if frame_bgr_numpy is None:
        return None, []
    
    # Encode frame to JPG format to send as a file
    is_success, buffer = cv2.imencode(".jpg", frame_bgr_numpy)
    if not is_success:
        st.warning("Failed to encode frame to JPG format.")
        return frame_bgr_numpy.copy(), []
    
    files = {"file": ('frame.jpg', io.BytesIO(buffer), 'image/jpeg')}
    annotated_image_from_api_np = frame_bgr_numpy.copy()
    detections_from_api_list = []

    try:
        with st.spinner("Processing frame with API..."):
            response = requests.post(PROCESS_FRAME_ENDPOINT, files=files, timeout=20)
            response.raise_for_status()  # Raises an exception for HTTP erros
            api_data = response.json()

        logger.info(f"API Response: {api_data}")
        detections_from_api_list = api_data.get("detections", [])

        api_image_url = api_data.get("output_image_url")
        if api_image_url:
            full_url = f"{FLASK_API_BASE_URL}/{api_image_url}" if api_image_url.startswith("/") else api_image_url
            img_response = requests.get(full_url, stream=True, timeout=10)
            img_response.raise_for_status()
            pil_image = Image.open(io.BytesIO(img_response.content))
            annotated_image_from_api_np = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        elif "warning" in api_data:
            st.warning(f"API Warning: {api_data['warning']}")
    except requests.exceptions.HTTPError as e_http:
        st.error(f"Api Error: {e_http.response.status_code} - {e_http.response.text}")
    except requests.exceptions.RequestException as e_req:
        st.error(f"Connection Error: {e_req}")
    except Exception as e:
        st.error(f"Client Side Processing Error: {e}")
        logger.error(f"Error after API call: {e}", exc_info=True)
    
    return annotated_image_from_api_np, detections_from_api_list


# Sidebar UI ------
st.sidebar.header("Transaction Managment")
current_txn_id_for_input = st.session_state.active_transaction_id_ui if st.session_state.active_transaction_id_ui \
    else ""
txn_id_input_val = st.sidebar.text_input(
    "Transaction ID:",
    value=current_txn_id_for_input,
    disabled=(st.session_state.active_transaction_id_ui is not None)  # Disable if transaction is active
)

col_start, col_stop = st.sidebar.columns(2)
with col_start:
    if st.button("Start Transaction", disabled=(st.session_state.active_transaction_id_ui is not None or not \
                                                txn_id_input_val)):
        if txn_id_input_val:
            res = call_start_transaction_api(txn_id_input_val)
            if "error" in res:
                st.sidebar.error(res.get("message", "Start failed."))
            else:
                st.session_state.active_transaction_id_ui = txn_id_input_val
                st.sidebar.success(res.get("message"))
                st.experimental_rerun()
with col_stop:
    if st.button("🛑 Stop Transaction", disabled=(st.session_state.active_transaction_id_ui is not None)):
        res = call_stop_transaction_api(st.session_state.active_transaction_id_ui)
        if "error" in res:
            st.sidebar.error(res.get("message", "Stop failed."))
        else:
            st.sidebar.info(res.get("message"))
            st.session_state.active_transaction_id_ui = None
            st.session_state.last_processed_annotated_image = None  # Clear results
            st.session_state.last_detection_details_from_api = None
            if st.session_state.video_capture_object:  # Release cam on stop
                st.session_state.video_capture_object.release()
                st.session_state.video_capture_object = None
            st.experimental_rerun()

st.sidebar.header("Camera Controls")
st.session_state.camera_source_input = st.sidebar.text_input(
    "Camera Source (URL/Index):",
    value=st.session_state.camera_source_input,
    disabled=(st.session_state.video_capture_object is not None)  # Disable if video capture is active
)

if st.session_state.video_capture_object is None:
    if st.sidebar.button("📌 Connect Camera"):
        if st.session_state.camera_source_input:
            try:
                source = int(st.session_state.camera_source_input)
            except ValueError:
                source = st.session_state.camera_source_input
                temp_cap = cv2.VideoCapture(source)
                if temp_cap.isOpened():
                    st.session_state.video_capture_object = temp_cap
                    st.sidebar.success(f"Connected to camera source: {source}")
                else:
                    st.sidebar.error(f"Failed to connect to camera source: {source}")
                    temp_cap.release()
                st.experimental_rerun()
else:  # Camera is conne
    st.sidebar.info(f"Connected to: {st.session_state.camera_source_input}")
    if st.sidebar.button("🔌 Disconnect Camera"):
        st.session_state.video_capture_object.release()
        st.session_state.video_capture_object = None
        st.session_state.last_processed_annotated_image = None
        st.sidebar.info("Camera disconnected.")
        st.experimental_rerun()

# ------- Main UI ------
status_col, feed_col, result_col = st.columns([1, 2, 1])
with status_col:  # Display current status
    st.subheader("System Status")
    if st.session_state.active_transaction_id_ui:
        st.success(f"Txn Active: {st.session_state.active_transaction_id_ui}")
    else:
        st.warning("Txn Inactive")
    st.info(f"Camera: {'Connected' if st.session_state.video_capture_object else 'Disconnected'}")

with feed_col:  # Camera feed and capture button
    st.subheader("Camera / Processed Feed")
    image_placeholder = st.empty()
    actions_button_placeholder = st.empty()  # For capture or Resume

with result_col:  # Verification results
    st.subheader("Verification Details")
    results_display_area = st.container(height=600)  # Fixed height scrollable container for results

# Display and interaction logic
current_live_frame = None  # To hold the latest frame from the camera if active

if st.session_state.video_capture_object and st.session_state.video_capture_object.isOpened():
    ret, frame = st.session_state.video_capture_object.read()
    if ret and frame is not None:
        current_live_frame = frame.copy()
    else:
        st.session_state.display_mode = "show_result"  # If camera fails, revert to showing last result
        image_placeholder.warning("Error reading from camera.")
    
# Decide what to display in the main image placeholder
if st.session_state.display_mode == "show_result" and st.session_state.result_annotated_image is not None:
    image_placeholder.image(st.session_state.result_annotated_image, channels="BGR",
                            use_colum_width=True, caption="Processed Verification Result")
elif current_live_frame is not None:  # Display live feed if available and not showing result
    image_placeholder.image(current_live_frame, channels="BGR",
                            use_column_width=True, caption="Live camera feed")
elif st.session_state.video_capture_object:  # Camera connected but no frame yet or error
    image_placeholder.info("Waiting for camera feed...")
else:  # No camera connected
    image_placeholder.info("Connect camera to start live feed.")

# Actions button logic
with actions_button_placeholder:
    if st.session_state.active_transaction_id_ui:  # Transaction must be active
        if st.session_state.display_mode == "show_result":
            if st.button("🔄 Resume Live Feed", key="resume_feed_btn", use_container_width=True):
                st.session_state.display_mode = "live_feed"
                st.session_state.result_annotated_image = None  # clear old result
                st.session_state.result_detection_details = None
                st.experimental_rerun()
        elif current_live_frame is not None:  # Display mode is live feed and camera is providing frames
            if st.button("📸 Capture & Verify Customer", key="capture_btn", use_container_width=True):
                # Set display mode to show result before API call, so UI updates correctly after
                st.session_state.display_mode = "show_result"

                # Show spinner over the current live frame area while processing
                with image_placeholder:
                    st.image(current_live_frame, channels="BGR",
                             use_column_width=True, caption="Processing Frame...")
                    with st.spinner("Sending to API for verification..."):
                        api_annotated_image, api_detections = call_process_frame_api(current_live_frame)
                    
                    st.session_state.result_annotated_image = api_annotated_image
                    st.session_state.result_detection_details = api_detections
                    st.experimental_rerun()  # Rerun to display the results now stored in session state
    else:  # No active transaction
        st.info("Start a transaction to enable capture and verification.")


# Displaying the detection results from session state
with results_display_area:
    if st.session_state.display_mode == "show_result" and st.session_state.result_detection_details is not None:
        detection_list = st.session_state.result_detection_details
        if not detection_list:
            st.info("No relevant detections in the processed image.")
        
        for detail in detection_list:
            # Assuming API now only sends 'face types or we can filter here
            if detail.get("type") == "face":
                st.markdown(f"**Face Analysis")

                # Age display
                age_label = detail.get('age_label', 'N/A')
                age_color = "green" if "adult" in age_label.lower() else "red" if "child" in age_label.lower() else "gray"
                st.markdown(f" Age Estimate: <span style='color:{age_color}; font-weight:bold;'>{age_label}</span>",
                            unsafe_allow_html=True)
                if detail.get('age_model_confidence') is not None:
                    st.markdown(f"  Age Confidence: {detail['age_model_confidence']:.2f}")
                
                # Face matching status
                status = detail.get('face_status', 'N/A')
                face_id_short = str(detail.get('face_id', '')).split(':')[-1][:8]  # Short ID
                sim = detail.get('similarity')
                sim_text = f"(Similarity: {sim:.2f})" if status == 'seen' and sim is not None else ""
                status_color = "blue" if status == 'new' else "orange" if status == 'seen' else "grey"
                if status not in ["not_processed_for_matching", "face_matching_services_unavailable",
                                  "face_matching_services_down"]:
                    st.markdown(f"""  Identity Check: <span style='color:{status_color};'>{status.replace('_','').capitalize()}</span>
                                 {sim_text} (Ref: {face_id_short})""", unsafe_allow_html=True)
                elif status != "not_processed_for_matching":  # Only show if it's an actual status, not the default
                    st.markdown(f" Identity Check: {status.replace('_', '').capitalize()}")
                st.markdown("-----")
    elif st.session_state.display_mode == "live_feed":
        results_display_area.empty()    # Clear results area when in live feed mode
        results_display_area.info("Capture an image to see verification details.")

# Rerun for live feed if in that mode and camera is active
if st.session_state.display_mode == "live_feed" and \
        st.session_state.video_capture_object and \
        st.session_state.video_capture_object.isOpened():
    time.sleep(0.1)  # Small delay to avoid rapid reruns
    st.experimental_rerun()  # Rerun to keep the live feed updating
