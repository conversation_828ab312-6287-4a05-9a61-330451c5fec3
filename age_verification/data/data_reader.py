import os
import numpy as np
from enum import Enum
from typing import Dict, Generator, List, Optional, Tuple

import cv2

IMAGES_EXT: Tuple = (".jpeg", ".jpg", ".png", ".webp", ".bmp", ".gif")
VIDEO_EXT: Tuple = (".mp4", ".aiv")


def get_all_files(path: str, extensions: Tuple = IMAGES_EXT):
    files_all = []
    for root, subfolders, files in os.walk(path):
        for name in files:
            # linux tricks with directory that still is file
            if "directory" not in name and sum([ext.lower() in name.lower() for ext in extensions]) > 0:
                files_all.append(os.path.join(root, name))
    
    return files_all


class InputType(Enum):
    Image = 0
    Video = 1
    VideoStream = 2


def get_input_type(input_path: str) -> InputType:
    if os.path.isdir(input_path):
        print("Input is folder only images will be processed")
        return InputType.Image
    elif os.path.isfile(input_path):
        if input_path.endswith(VIDEO_EXT):
            return InputType.Video
        if input_path.endswith(IMAGES_EXT):
            return InputType.Image
        else:
            raise ValueError(
                f"Unknown or unsupported input file format {input_path}"
            )
    elif input_path.startswith(("http://", "https://", "rtsp://")):
        return InputType.VideoStream
    else:
        raise ValueError(f"Unknown input {input_path}")
    

class VideoReder:
    
    def __init__(self, source: str):
        """
        Initialize video reader for video files or streams

        Args:
            source: Path to video file url to video stream
        """
        self.source = source
        self.cap = None
        self.frame_count = 0
        self.width = 0
        self.height = 0
        self.fps = 0

    def __enter__(self):
        self.cap = cv2.VideoCapture(self.source)
        if not self.cap.isOpened():
            raise ValueError(f"Failed to open video source")

        # Get video properties
        self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        self.fps = int(self.cap.get(cv2.CAP_PROP_FPS))

        if not self.is_stream():
            self.frame_count = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))

        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.cap is not None:
            self.cap.release()

    def is_stream(self) -> bool:
        """Check if source is a stream"""
        return self.source.startswith(("rtsp://", "http://", "https://"))

    def read_frames(self) -> Generator[Tuple[bool, Optional[np.ndarray]], None, None]:
        """
        Generator that yields frames from the video source
        Returns:
            Tuple of (success, frame)
        """
        while True:
            if self.cap is None:
                raise RuntimeError("Video reader is not initialized.")
            
            success, frame = self.cap.read()
            if not success:
                if self.is_stream():
                    # for streams try to reconnect
                    self.cap.release()
                    self.cap = cv2.VideoCapture(self.source)
                    if not self.cap.isOpened():
                        yield False, None
                        continue
                else:
                    # End of file
                    break
            yield success, frame


def get_video_reader(source: str) -> VideoReder:
    """
    Creates a video reader for given source
    """
    return VideoReder(source)
