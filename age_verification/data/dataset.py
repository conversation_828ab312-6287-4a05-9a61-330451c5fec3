#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional

import torch
from PIL import Image
from torch.utils.data import Dataset
from torchvision import transforms

_logger = logging.getLogger(__name__)


class PreCroppedFaceDataset(Dataset):
    def __init__(self,
                 data_root_dir: str,  # Root dir where crop images are stored
                 manifest_file: str,  # annotations.json for crops
                 split: str = "train",
                 target_size: Tuple[int, int] = (224, 224),
                 # If already augmented, transform might just be Resize, ToTensor, Normalize
                 transform: Optional[transforms.Compose] = None):
        self.data_root_dir = data_root_dir
        self.manifest_file = manifest_file
        self.split = split
        self.target_size = target_size
        self.samples = []  # List to store filtered samples for the current split

        try:
            with open(self.manifest_file, 'r') as f:
                loaded_json_data = json.load(f)
            all_annotations_list = []

            # if "annotations" not in loaded_json_data or not isinstance(loaded_json_data["annotations"], list):
            #     _logger.error(f"Manifest JSON {self.manifest_file} is not in the expected format (missing 'annotations' list)")
            # else:
            #     all_annotations_list = loaded_json_data["annotations"]
            
            if isinstance(loaded_json_data, list):
                # already a list use directly
                all_annotations_list = loaded_json_data
                _logger.debug(f"Manifest JSON {self.manifest_file} is a list. Using it directly.")
            elif isinstance(loaded_json_data, dict) and "annotations" in loaded_json_data \
                 and isinstance(loaded_json_data["annotations"], list):
                # If it's a dictionary with an "annotations" key containing a list
                all_annotations_list = loaded_json_data["annotations"]
                _logger.debug(f"Manifest JSON {self.manifest_file} is a dict. Extracted 'annotations' list.")
            else:
                _logger.error(f"Manifest JSON {self.manifest_file} is not in an expected format. "
                              f"Expected a list of annotations, or a dictionary with an 'annotations' key "
                              f"containing a list. Found type: {type(loaded_json_data)}")
            
            if all_annotations_list:
                for ann in all_annotations_list:
                    if isinstance(ann, dict) and ann.get('split') == self.split:
                        if ('image_path' in ann and \
                            'ages' in ann and isinstance(ann['ages'], list) and len(ann['ages']) > 0):
                                # 'genders' in ann and isinstance(ann['genders'], list) and len(ann['genders']) > 0):
                            self.samples.append(ann)
                        else:
                            _logger.warning(f"Skipping annotation due to missing or invalid required keys: {ann}")

            _logger.info(f"Loaded and filtered {len(self.samples)} samples for split '{split}' from {self.manifest_file}")
            if len(self.samples) == 0:
                _logger.warning(f"No samples found for split '{split}' in {self.manifest_file}. Dataset will be empty.")
        except FileNotFoundError:
            _logger.warning(f"Manifest file {self.manifest_file} not found. Dataset will be empty.")
        except json.JSONDecodeError:
            _logger.error(f"Error decoding JSON file from {self.manifest_file}. Dataset will be empty.")
        except Exception as e:
            _logger.error(f"Error loading or parsing manifest file {self.manifest_file}: {e}. Dataset will be empty.")
        
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize(self.target_size),
                transforms.ToTensor(),  # Converts PIL Image [0, 255] HWC to Tensor [0, 1] CHW
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transform
        
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, Dict, Optional[Tuple[int, int]]]:
        if not self.samples or idx >= len(self.samples):
            _logger.error(f"""Attempting to get item at index {idx} but dataset has {len(self.samples)} samples for 
                          split' {self.split}. Raising Index error""")
            raise IndexError(f"Index {idx} out of bounds for dataset split {self.split} with length {len(self.samples)}")
        
        sample_info = self.samples[idx]
        img_path = sample_info['image_path']
        # Ensure ages and genders are list and take the first element
        gt_age_raw = sample_info['ages'][0] if sample_info['ages'] else None
        gt_gender_int = sample_info['genders'][0] if sample_info['genders'] else None

        # 0: male, 1: female, -1:unknown ?

        try:
            crop_image_pill = Image.open(img_path).convert("RGB")
        except FileNotFoundError:
            _logger.error(f"Image file not found: {img_path}, Returning dummy data.")
            # Return dummy data that won't cause NaN during normalization/loss if possible
            # samll random noise
            dummy_tensor = torch.rand((3, self.target_size[0], self.target_size[1]), dtype=torch.float32) * 0.1
            # Dummy must have same structure
            dummy_target = {"ages": [-1], "genders": ["unknown"], "is_pre_cropped": True, "error_loading": True}
            return dummy_tensor, dummy_target, self.target_size
        except Exception as e:
            _logger.error(f"Error loading image {img_path}, {e}. Returning dummy data.", exc_info=True)
            dummy_tensor = torch.rand((3, self.target_size[0], self.target_size[1]), dtype=torch.float32) * 0.1
            dummy_target = {"ages": [-1], "genders": ["unknown"], "is_pre_cropped": True, "error_loading": True}
            return dummy_tensor, dummy_target, self.target_size
        
        original_crop_size = (crop_image_pill.height, crop_image_pill.width)  # H, W for PIL

        if self.transform:
            crop_image_tensor = self.transform(crop_image_pill)
        else:
            # If no transform assume already a tensor
            _logger.warning(f"No transform provided for dataset of split {self.split}. Attempting ToTensor manually.")
            crop_image_tensor = transforms.ToTensor()(crop_image_pill)
        
        target_dict = {
            "ages": [gt_age_raw],
            "genders": [gt_gender_int],
            "face_boxes": torch.empty(0, 4),  # Not relevant for pre-cropped to model
            "is_pre_cropped": True
        }

        return crop_image_tensor, target_dict, original_crop_size


class AgeGenderDataset(Dataset):
    """
    Dataset class for age and gender detection.
    
    This dataset loads images and their corresponding age and gender annotations.
    It supports both face-only and face+person modes.
    """

    def __init__(
        self,
        data_path: str,
        split: str = "train",
        transform: Optional[transforms.Compose] = None,
        target_size: Tuple[int, int] = (224, 224),
        with_persons: bool = False,
        disable_faces: bool = False,
        min_age: int = 0,
        max_age: int = 100,
        avg_age: float = 50.0,
        only_age: bool = False
    ):
        """
        Initialize the dataset.
        
        Args:
            data_path: Path to the dataset directory
            split: Dataset split ('train', 'val', or 'test')
            transform: Optional transforms to apply to the images
            target_size: Target size for the images (height, width)
            with_persons: Whether to include person crops
            disable_faces: Whether to disable face crops
            min_age: Minimum age in the dataset
            max_age: Maximum age in the dataset
            avg_age: Average age in the dataset
            only_age: Whether to predict only age (no gender)
        """
        self.data_path = Path(data_path)
        self.split = split
        self.target_size = target_size
        self.with_persons = with_persons
        self.disable_faces = disable_faces
        self.min_age = min_age
        self.max_age = max_age
        self.avg_age = avg_age
        self.only_age = only_age

        # Set up transforms
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((target_size)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transform
        
        # Load annotations
        self.annotations = self._load_annotations()
        
        # Filter annotations based on split
        self.annotations = [ann for ann in self.annotations if ann["split"] == split]

        # # Filter to remove empty face boxes
        # original_count = len(self.annotations)
        # self.annotations = [ann for ann in self.annotations if ann.get("face_boxes") and len(ann["face_boxes"]) > 0]
        # filtered_count = len(self.annotations)
        # if original_count != filtered_count:
        #     logging.info(f"Filtered out {original_count - filtered_count} samples from '{split}' split due to no face boxes.")
        
        print(f"Loaded {len(self.annotations)} {split} samples")
    
    def _load_annotations(self) -> List[Dict]:
        """
        Load annotations from the dataset.
        
        Returns:
            List of annotation dictionaries
        """
        annotations = []
        
        # Check if annotations file exists
        annotations_file = self.data_path / "annotations.json"
        if annotations_file.exists():
            with open(annotations_file, "r") as f:
                annotations = json.load(f)
        else:
            # If no annotations file, scan the directory structure
            for img_path in self.data_path.glob("**/*.jpg"):
                # Skip if not in the correct split directory
                if self.split not in str(img_path):
                    continue
                
                # Create annotation
                annotation = {
                    "image_path": str(img_path.relative_to(self.data_path)),
                    "split": self.split,
                    "ages": [],
                    "genders": [],
                    "face_boxes": [],
                    "person_boxes": []
                }
                
                # Try to load corresponding annotation file
                ann_path = img_path.with_suffix(".json")
                if ann_path.exists():
                    with open(ann_path, "r") as f:
                        ann_data = json.load(f)
                        annotation.update(ann_data)
                
                annotations.append(annotation)
        
        return annotations
    
    def __len__(self) -> int:
        """Return the number of samples in the dataset."""
        return len(self.annotations)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, Dict, Tuple[int, int]]:
        """
        Get a sample from the dataset.
        
        Args:
            idx: Index of the sample
            
        Returns:
            Tuple of (image, target, original_size)
            image: Processed image tensor (3, H, W)
            target: Dict containing ground truth labels and boxes in original coordinates
            original_size: Tuple Original image size (H, W)
        """
        annotation = self.annotations[idx]
        
        # Load the original image first to get dimenstions before resizing
        img_path = self.data_path / annotation["image_path"]
        original_image_pil = None
        original_size: Tuple[int, int] = (64, 64)  # default fallback size as a tuple
        try:
            # load actual image
            original_image_pil = Image.open(img_path).convert("RGB")
            original_width, original_height = original_image_pil.size
            # store as (H, W) tuple
            original_size = (original_height, original_width)
        except (IOError, OSError) as e:
            print(f"Error loading Image: {img_path}: {str(e)}")
            original_image_pil = Image.new("RGB", (64, 64), (0, 0, 0))
            original_size = (64, 64)
        
        # Apply transforms
        try:
            image_tensor = self.transform(original_image_pil)
        except Exception as e:
            print(f"Error applying transforms to Image: {img_path}: {str(e)}")
            # create a black image
            target_h, target_w = self.target_size if hasattr(self, 'target_size') else (64, 64)
            image_tensor = torch.zeros(3, target_h, target_w, dtype=torch.float32)
        
        # Prepare target
        target = {
            "ages": torch.tensor(annotation["ages"]),
            "genders": annotation["genders"],
            "face_boxes": torch.tensor(annotation["face_boxes"]) if annotation["face_boxes"]
            else torch.zeros((0, 4)),
            "person_boxes": torch.tensor(annotation["person_boxes"]) if annotation["person_boxes"]
            else torch.zeros((0, 4))
        }
        
        return image_tensor, target, original_size
    
    def get_meta(self) -> Dict:
        """
        Get dataset metadata.
        
        Returns:
            Dictionary containing dataset metadata
        """
        return {
            "min_age": self.min_age,
            "max_age": self.max_age,
            "avg_age": self.avg_age,
            "only_age": self.only_age,
            "with_persons": self.with_persons,
            "disable_faces": self.disable_faces
        }
