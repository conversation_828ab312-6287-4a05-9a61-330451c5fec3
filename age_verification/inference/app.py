from inference.config import get_config
from flask import Flask
from logging.handlers import RotatingFileHandler
import logging
import os
os.environ['CUDA_VISIBLE_DEVICES'] = "-1"


def create_app():
    app = Flask(__name__)
    app.config.from_object(get_config())

    log_level = getattr(logging, app.config['LOG_LEVEL'], logging.INFO)
    app_file_handler = RotatingFileHandler(os.path.join(
        app.config['LOGS_DIR'], 'app.log'), maxBytes=10240, backupCount=10)
    app_file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s: - %(message)s [in %(pathname)s:%(lineno)d]'))
    app_file_handler.setLevel(log_level)
    # use flask's app.logger
    app.logger.addHandler(app_file_handler)
    app.logger.setLevel(log_level)

    # stdout logging, for dev/Docker
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s: - %(message)s'))
    stream_handler.setLevel(log_level)
    app.logger.addHandler(stream_handler)

    app.logger.info('Flask app configured and starting up...')
    app.logger.info(f"Running with FLASK_ENV: {os.getenv('FLASK_ENV', 'development')}")
    app.logger.info(f"Device set to: {app.config['DEVICE']}")

    from services import initialize_global_services
    initialize_global_services(app)

    # define and regiser Blueprint
    from flask import Blueprint
    # if routes become complex, move into separate file
    transaction_api_bp = Blueprint('transaction_api', __name__)

    # move all route definations and process function
    # into this blueprint definition
    # content related to API endpoints and logic goes here
    from transaction_routes_blueprint import bp as transaction_routes_blueprint
    app.register_blueprint(transaction_routes_blueprint)

    app.logger.info("Flask app created and blueprints registered.")
    return app


if __name__ == '__main__':
    app = create_app()
    app.run(host="0.0.0.0", port=5000)


"""
Redis transaction key stores a pickled dictionary {'id': 'txn_id', 'start_time': ts}
/start_transaction sets key in Redis
/process_frame reads this key to know the active transaction ID
/stop_transaction deletes this key
Approach makes the active transaction state shared across multiple Flask workers


---
Loggers are fetched/created per request for the active transaction id obtained from Redis
"""
