import os


class Config:
    """Configuration class for the inference script."""
    SECRECT_KEY = os.getenv("SECRECT_KEY", "your_secret_key")
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

    # Application specific configurations
    MIVOLO_MODEL_PATH = os.getenv("MIVOLO_MODEL_PATH", "finetuned-MiVOLO/best_model.pth")
    MIVOLO_DETECTOR_WEIGHTS = os.getenv("MIVOLO_DETECTOR_WEIGHTS", "models/yolov8x_person_face.pt")

    OUTPUT_DIR = os.getenv("OUTPUT_DIR", "testing/output_dir/")
    TEMP_DIR = os.getenv("FLASK_TEMP_DIR", "temp_uploads")
    LOGS_DIR = os.getenv("FLASK_LOGS_DIR", "transaction_logs/")

    # Model Device and Precision
    DEVICE = "cuda" if os.getenv('USE_CPU', 'false').lower() != 'true' and __import__('torch').cuda.is_available() else "cpu"
    USE_HALF_PRECISION = os.getenv('USE_HALF_PRECISION', 'true').lower() == 'true' and DEVICE == "cuda"
    DRAW_OUTPUT = os.getenv('DRAW_OUTPUT', 'true').lower() == 'true'

    # Redis Configuration
    REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
    REDIS_DB = int(os.getenv("REDIS_DB", 0))
    REDIS_TRANSACTION_KEY = "active_transaction_details"  # key to store active transaction details

    # Face embedding and Matching
    FACE_EMBEDDING_MODEL = os.getenv("FACE_EMBEDDING_MODEL", "Facenet")
    SIMILARITY_THRESHOLD = float(os.getenv("SIMILARITY_THRESHOLD", 0.65))
    FACE_TTL_SECONDS = int(os.getenv("FACE_TTL_SECONDS", 1 * 60))  # 1 minute

    # For serving processed images via Flask route
    SERVED_OUTPUT_ALIAS = "output"


class DevelopmentConfig(Config):
    DEBUG = True
    LOG_LEVEL = "DEBUG"


class ProductionConfig(Config):
    DEBUG = False
    "e.g. different redis host from a secure source"


# helper to get the config object based on the env variable
def get_config():
    env = os.getenv('FLASK_ENV', 'development').lower()
    if env == 'procution':
        return ProductionConfig()
    return DevelopmentConfig()


current_config = get_config()
os.makedirs(current_config.OUTPUT_DIR, exist_ok=True)
os.makedirs(current_config.TEMP_DIR, exist_ok=True)
os.makedirs(current_config.LOGS_DIR, exist_ok=True)
