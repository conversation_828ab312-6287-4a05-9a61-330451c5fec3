import logging
import os
from typing import Dict, Tuple, Union, List, Optional

import PIL.Image
import numpy as np
import torch
import cv2

from ultralytics import YOL<PERSON>
from mivolo.structures import PersonAndFaceResult
from ultralytics.engine.results import Results, Boxes

# because of ultralytics bug it is important to unset CUBLAS_WORKSPACE_CONFIG after the module importing
os.unsetenv("CUBLAS_WORKSPACE_CONFIG")

_logger = logging.getLogger("__name__")
pil_img_type = PIL.Image.Image


class Detector:
    def __init__(
            self, weights: str,
            device: str = "cuda", half: bool = True,
            verbose: bool = False,
            conf_thresh: float = 0.25,
            iou_thresh: float = 0.45):

        self.yolo = YOLO(weights)
        self.yolo.fuse()

        self.device = torch.device(device)
        self.half = half and self.device.type != "cpu"

        if self.half:
            self.yolo.model = self.yolo.model.half()

        self.detector_names: Dict[int, str] = self.yolo.names

        # init yolo.predictor
        self.detector_kwargs = {"conf": conf_thresh, "iou": iou_thresh, "half": self.half, "verbose": verbose}

    def eval(self):
        """set the model to evaluation mode"""
        self.yolo.model.eval()

    def _process_json_boxes(
        self,
        json_boxes: Dict[str, Union[list, torch.Tensor]],
    ) -> Dict[str, List[List[float]]]:
        """
        Process input JSON boxes which can be lists or tensors into consistent
        format list of floats and fixes zer-dimension boxes.
        """
        processed_boxes: Dict[str, Union[list, torch.Tensor]] = {
            "face_boxes": [],
            "person_boxes": []
        }

        raw_face_boxes = json_boxes.get("face_boxes", [])
        raw_person_boxes = json_boxes.get("person_boxes", [])

        # process face boxes
        if isinstance(raw_face_boxes, torch.Tensor):
            raw_face_boxes = raw_face_boxes.detach().cpu().tolist()
        elif not isinstance(raw_face_boxes, list):
            logging.warning(f"Face box is neither tensor nor list, got {type(raw_face_boxes)}. Using empty list.")
            raw_face_boxes = []
        
        for i, box in enumerate(raw_face_boxes):
            if not isinstance(box, (list, tuple, np.ndarray)) or len(box) != 4:
                logging.warning(f"Skipping invalid face box format at index {i}: {box}")
                continue
            try:
                box = [float(c) for c in box]
            except (ValueError, TypeError):
                logging.warning(f"Skipping invalid face box format at index {i}: {box}")
                continue

            x1, y1, x2, y2 = box
            fixed_box = list(box)  # start with a copy
            dimensions_fixed = False
            # Fix zero or negative dimensions by adding a samll epsilon eg. 1 px
            if x1 >= x2:
                fixed_box[2] = x1 + 1.0  # Ensure at least one pixel width
                dimensions_fixed = True
            if y1 >= y2:
                fixed_box[3] = y1 + 1.0  # Ensure at least one pixel height
                dimensions_fixed = True
            if dimensions_fixed and _logger.level <= logging.DEBUG:
                logging.debug(f"Fixed face box at index {i}: {box} -> {fixed_box}")
            processed_boxes["face_boxes"].append(fixed_box)

        return processed_boxes

    def create_from_json_boxes(
        self,
        # used for copping e.g. 224x224 np array
        image: Union[np.ndarray, str, pil_img_type],
        json_boxes: Dict[str, Union[list, torch.tensor]],
        # require original image sahpe when using jsob boxes
        original_image_shape: Union[Tuple[int, int], List[int]]  # Accepts list or tuple
    ) -> Optional[PersonAndFaceResult]:
        """
        Create a PersonAndFaceResult directly from JSON boxes without running YOLO detection.

        Args:
            image,
            json_boxes: Dictionary contining face_boxes and person_boxes lists

        PersonAndFaceResult object create from JSON boxes, or None if no valid boxes.
        """
        if json_boxes is None:
            logging.debug("No JSON boxes provided.")
            return None
        logging.debug(f"Original image shape: {original_image_shape}")

        orig_shape_tuple: Optional[Tuple[int, int]] = None
        # Accepts list or tuple, check length and content
        if original_image_shape is not None and isinstance(original_image_shape, (list, tuple)) and len(original_image_shape) == 2:
            try:
                h, w = int(original_image_shape[0]), int(original_image_shape[1])
                if h > 0 and w > 0:
                    orig_shape_tuple = (h, w)  # Store as tuple
            except (ValueError, TypeError):
                _logger.warning(f"Invalid original image shape: {original_image_shape}")

        if orig_shape_tuple is None:
            logging.warning("create_from_json_boxes: requires valid positive original image shape. Invalid original image shape. Returning None.")
            return None
        
        # if validation passed, proceed..
        _logger.debug(f"Using original image shape: {orig_shape_tuple} for JSON boxes.")

        # Determine target image shape for scaling
        target_img_h, target_img_w = None, None
        orig_img_for_results = None  # Need the target image array

        # Load/convert the image argument into HWC uint8 array
        # This is the image boxes need to be scaled to 224x224
        if isinstance(image, np.ndarray):
            img_np = image
        elif isinstance(image, pil_img_type):
            img_np = np.array(image.convert("RGB"))
        elif isinstance(image, str):
            img_np = cv2.imread(image)
            if img_np is not None:
                img_np = cv2.cvtColor(img_np, cv2.COLOR_BGR2RGB)
        else:
            _logger.debug(f"Unsupported image type: {type(image)}. Returning None")
            return None
        
        if img_np is None:
            _logger.error("Failed to load/get image array. Returning None")
            return None
        
        if img_np.ndim == 3 and img_np.shape[2] == 3:
            if img_np.dtype != np.uint8:
                _logger.warning(f"Input image has dtype {img_np.dtype}. Converting to uint8")

                try:
                    orig_img_for_results = (img_np.astype(np.float32) * 255.0).clip(0, 255).astype(np.uint8)
                except Exception as e:
                    _logger.error(f"Error converting image to uint8: {e}. Returning None")
                    return None
            else:
                orig_img_for_results = img_np
        else:
            _logger.error(f"Invalid image shape: {img_np.shape}. Returning None")
            return None
        
        target_img_h, target_img_w = orig_img_for_results.shape[:2]
        _logger.debug(f"Target image shape for scaling/Results: ({target_img_h}, {target_img_w}")
        
        # calculate scaling factors
        orig_h, orig_w = orig_shape_tuple
        scal_h = target_img_h / orig_h
        scal_w = target_img_w / orig_w
        _logger.debug(f"Scaling factors: h: {scal_h}, w: {scal_w}")

        # process and fix zero/negative dimensions in json boxes
        # This returns lists of lists of floats
        processed_json_boxes = self._process_json_boxes(json_boxes)
        face_boxes_list = processed_json_boxes["face_boxes"]
        person_boxes_list = processed_json_boxes["person_boxes"]

        if not face_boxes_list and not person_boxes_list:
            logging.debug("No valid JSON boxes provided. Returning None.")
            return None
        
        # Prepare data for ultralytics.engine.results.Boxes constructor
        # format: [x1, y1, x2, y2, confidence, class]
        boxes_data = []
        confidence = 1.0  # confidence for ground truth boxes

        # map class names to integer ids needed by boxes
        # we need to find the integer ids that map to face and person in seld.detector_names
        # Assumes 1 is face, 0 is person
        # lets search for dict keys/values
        face_cls_id = None
        person_cls_id = None
        for cls_id, cls_name in self.detector_names.items():
            if cls_name == "face":
                face_cls_id = cls_id
            elif cls_name == "person":
                person_cls_id = cls_id

        if face_cls_id is None or person_cls_id is None:
            logging.warning("Could not find face or person class ids in detector names. Returning None.")
            return None
        
        # add face boxes
        if face_cls_id is not None:
            for box in face_boxes_list:
                x1, y1, x2, y2 = box
                # apply scaling
                scaled_x1 = x1 * scal_w
                scaled_y1 = y1 * scal_h
                scaled_x2 = x2 * scal_w
                scaled_y2 = y2 * scal_h
                boxes_data.append([scaled_x1, scaled_y1, scaled_x2, scaled_y2, confidence, float(face_cls_id)])
        else:
            logging.warning("Skipping face boxes: 'face' class not found in detector names.")
            return None
        
        if person_cls_id is not None:
            for box in person_boxes_list:
                boxes_data.append([*box, confidence, float(person_cls_id)])
            _logger.debug(f"added {len(person_boxes_list)} person boxes. with class ID {person_cls_id}")
        else:
            _logger.warning("Skipping person boxes: 'person' class not found in detector names.")

        if not boxes_data:
            _logger.warning("No box data prepared after mapping classs or lists were empty. Returning None.")
            return None

        try:
            # convert list of box data to tensor and move to device
            boxes_tensor = torch.tensor(boxes_data, dtype=torch.float32, device=self.device)
            logging.debug(f"""Created scaled boxes tensor Boxes constructor. 
                          Shape: {boxes_tensor.shape}: \n Dtype: {boxes_tensor.dtype}""")
            if _logger.level <= logging.DEBUG and boxes_tensor.shape[0] > 0:
                logging.debug(f"""First rows of scaled boxes tensor (raw JSON coord): \n
                              {boxes_tensor[:min(5, boxes_tensor.shape[0])].tolist()}:""")
        except Exception as e:
            logging.error(f"Error creating boxes tensor: {e}")
            return None

        # create the ultralytics.engine.results.Boxes object
        try:
            # critical pass the original image shape (H,W) as orig_shape
            # this tells the Boxes object that the coordinates in boxes_tensor
            # the raw json coords are relative to an image of this size
            target_shape_tuple = (target_img_h, target_img_w)
            boxes = Boxes(boxes_tensor, orig_shape=target_shape_tuple)
            logging.debug(f"Created boxes with orig_shape={target_shape_tuple}. Data shape: {boxes.data.shape}")

            # for the debugging check the shape of the image being pased to Results.orig_img
            # and verify scaling ratio
            results_img_size = None
            if isinstance(image, np.ndarray):
                results_img_size = (image.shape[0], image.shape[1])  # H, W
            elif isinstance(image, PIL.Image.Image):
                results_img_size = (image.size[1], image.size[0])  # PIL is W, H; Results expects H, W
            # Handle file path case need to load to get shape
            elif isinstance(image, str):
                try:
                    img_check = cv2.imread(image)
                    if img_check is not None:
                        results_img_size = (img_check.shape[0], img_check.shape[1])
                        img_check = None  # release memory
                    else:
                        logging.warning(f"Could not load image from path: {image}")
                except Exception as e:
                    logging.error(f"Error loading image from path: {image}:  to determine Results img size for debug {e}")
            
            logging.debug(f"Image passed to Results will have shape (H, W): {results_img_size}")
            if results_img_size and original_image_shape:
                scale_h = results_img_size[0] / original_image_shape[0] if original_image_shape[0] > 0 else 0
                scale_w = results_img_size[1] / original_image_shape[1] if original_image_shape[1] > 0 else 0
                logging.debug(f"Scaling ratio for Results image: H: {scale_h}, W: {scale_w} from {original_image_shape} to results img {results_img_size}")

            # Debugging Access the xyxyy property, which scales from orig_shape to results.orig_shape
            # This scaled tensor will be on the device of Boxes object
            if _logger.level <= logging.DEBUG and boxes.data.shape[0] > 0 and results_img_size:
                xyxy_scaled = boxes.xyxy  # Access xyxy should be on self.device
                _logger.debug(f"""First few rows for Boxes.xyxy (scaled to Results img size {results_img_size})
                {xyxy_scaled[:min(5, xyxy_scaled.shape[0])].tolist()}""")
        except Exception as e:
            logging.error(f"Error creating boxes object from tensor with orig_shape={original_image_shape}: {e}. Cannot create any dummy results.")
            return None

        # create a dummy Results object
        try:
            # pass the actual array being processed the poteintially resized one
            # e.g. 224x224 numpy array HWC
            # This image is what collect_crops will use and its shape is what the Boxes object
            # implicitly scales coordinates to when its .xyxy property is accessed within Results.
            orig_img_for_results = None
            if isinstance(image, str):
                # Load image from path for Results assuming this is the resized one handled by dataset
                img_np = cv2.imread(image)
                if img_np is not None:
                    orig_img_for_results = cv2.cvtColor(img_np, cv2.COLOR_BGR2RGB)
                else:
                    _logger.error(f"Failed to load image for Results object from path: {image}")
                    return None
            elif isinstance(image, PIL.Image.Image):
                orig_img_for_results = np.array(image.convert("RGB"))  # Convert PIl to HWC  numpy uint8
            elif isinstance(image, np.ndarray):
                # This is the expected case from Model Manager - the resized numpy array HWC uint8
                # Ensure its uint8, assuming it came from 0-1 float if not
                if image.dtype != np.uint8:
                    _logger.warning(f"""Image numpy array for Results object has dtype {image.dtype}, expected uint8.
                    Attempting conversion assuming 0-1 float range.""")
                try:
                    # Assume float32 [0,1] range and convert to uint8 [0, 255]
                    orig_img_for_results = (image.astype(np.float32) * 255.0).astype(np.uint8)
                except Exception as e:
                    _logger.error(f"Failed to convert image numpy array to uint8 for Results object: {e}")
                    return None
                else:
                    orig_img_for_results = image
            else:
                _logger.error(f"Unsupported image type for Results object: {type(image)}")
                return None

            if orig_img_for_results is None or orig_img_for_results.ndim != 3 or orig_img_for_results.shape[2] != 3 or orig_img_for_results.dtype != np.uint8:
                _logger.error(f"""Invalid image for Results object: {orig_img_for_results.shape} {orig_img_for_results.dtype}
                Expected (H, W, 3) uint8.""")
                return None

            # create the Results object
            results = Results(
                orig_img=orig_img_for_results,  # Pass the actual 224x224 numpy image here
                # orig_shape=target_shape_tuple,
                path="",
                names=self.detector_names,
                boxes=boxes_tensor  # Pass the Boxes object here which has og image shape set as orig_shape
            )
            # results.boxes = boxes
            _logger.debug(f"Created dummy Results object with orig_img_shpae: {results.boxes.orig_shape} and assosiated Boxes orig_shape {len(results.boxes.orig_shape)} boxes.")

            # wrap the Results object in PersonAndFaceResult uses result.boxes to get face/person objects.
            person_and_face_result = PersonAndFaceResult(results)
            _logger.debug(f"Wrapped Results in PersonAndFaceResult. Number of objects: {person_and_face_result.n_objects}")
            if _logger.level <= logging.DEBUG and person_and_face_result.n_objects > 0:
                _logger.debug(f"PersonAndFaceResult counts - Faces: {len(person_and_face_result.face_objects)}")
                # Log the coordinates of the first detected face
                # this coordinates should be correctly scaled to the size of results.orig_img 224x224 image
                if person_and_face_result.face_objects is not None and len(person_and_face_result.face_objects) > 0:
                    first_face_box_scaled = person_and_face_result.face_objects.xyxy[0].tolist()
                    _logger.debug(f"First face box coords from personAndFaceResult (scaled to {results.orig_img.shape[:2]}): {first_face_box_scaled}")
            
            return person_and_face_result
        except Exception as e:
            logging.error(f"Error creating dummy Results or wrapping in PersonAndFaceResult: {e}")
            return None

    def predict(
            self,
            # This image is the one used for inference/cropping e.g. 224x224
            image: Union[np.ndarray, str, pil_img_type],
            # json boxes coords belong to original image shape space
            json_boxes: Optional[Dict[str, Union[list, torch.tensor]]] = None,
            # original image shape H, W corresponding to json_boxes
            original_image_shape: Optional[Union[Tuple[int, int], List[int]]] = None  # og image shape
    ) -> Optional[PersonAndFaceResult]:
        """
        Run face detection on the image or use provided JSON boxes.

        Image - numpy, filepath or PIL image in 224x224 format.
        json_boxes: Dictionary containing face_boxes and person_boxes lists. asumed to be in original image coordinates.


        Returns:
            PersonAndFaceResult object containing detected faces and persons.
        """
        logging.debug(f"""Detecor predict called. Json boxes provided: {json_boxes is not None},
                      Original shape provided: {original_image_shape}""")
        # If json boxes are provided and use_json_boxes is enabled, use them
        if json_boxes is not None:
            result = self.create_from_json_boxes(image, json_boxes, original_image_shape)
            if result is not None:
                logging.debug(f"Successfully created results from JSON boxes with {result.n_objects} objects.")
                return result
            else:
                logging.warning("No valid JSON boxes provided. Running YOLO detection.")
        
        # If no valid JSON boxes, run YOLO detection
        results: Results = self.yolo.predict(image, **self.detector_kwargs)[0]
        return PersonAndFaceResult(results)

    def track(self, image: Union[np.ndarray, str, pil_img_type]) -> PersonAndFaceResult:
        results: Results = self.yolo.track(image, persist=True, **self.detector_kwargs)[0]
        return PersonAndFaceResult(results)
