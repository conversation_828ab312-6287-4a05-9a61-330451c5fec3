from typing import Tuple, Optional
import numpy as np
from ultralytics.engine.results import Results

from mivolo.structures import PersonAndFaceResult
from mivolo.model.mi_volo import MiVOLO
from mivolo.model.yolo_detector import Detector


class Predictor:
    def __init__(self, config, verbose: bool = False):
        self.detector = Detector(config.detector_weights, config.device, verbose=verbose)
        # Get half precision from config if available, otherwise use device based default
        half_precision = getattr(config, 'half', True if config.device == 'cuda' else False)
        self.age_gender_model = MiVOLO(
            config.checkpoint,
            config.device,
            half=half_precision,
            use_persons=config.with_persons,
            disable_faces=config.disable_faces,
            verbose=verbose
        )
        self.draw = config.draw
        # to maintaine state between frames
        self.current_batch = None
        # print("Predictor initialized")

    def recognize_image(self, image: np.ndarray) -> Tuple[PersonAndFaceResult, Optional[np.ndarray]]:
        """
        Process a single Image without batch processing
        """
        detected_objects: PersonAndFaceResult = self.detector.predict(image)

        # create output image with predictions
        out_im = image.copy()

        # Only proceed with age/gender predictions if we have detection
        if detected_objects.n_objects > 0:
            # run age/gender prediction
            self.age_gender_model.predict(image, detected_objects)

            # Draw annotations if requested
            if self.draw and detected_objects.yolo_results is not None:
                out_im = detected_objects.plot(
                    img=out_im,
                    ages=True,
                    labels=True,
                    genders=False
                )
        else:
            print("WARNING: No valid detections found in the image")
        return detected_objects, out_im
            
    def recognize(self, image: np.ndarray, json_boxes=None) -> Tuple[PersonAndFaceResult, Optional[np.ndarray]]:
        # If JSON boxes are provided, create PersonAndFaceResult directly without YOLO detection
        if json_boxes is not None:
            # Create empty Results object
            results = Results()
            results.boxes = json_boxes
            results.names = {0: "face", 1: "person"}  # Assuming these are the class indices
            detected_objects = PersonAndFaceResult(results)
        else:
            # Fall back to YOLO detection if no JSON boxes
            detected_objects: PersonAndFaceResult = self.detector.track(image)

        out_im = image.copy()

        # only proceed with age/gender prediction if we have detections
        if detected_objects.n_objects > 0:
            self.age_gender_model.predict(image, detected_objects)

            # if this is our first frame, initialize the batch
            if self.current_batch is None:
                print('Initializing first batch')
                self.current_batch = detected_objects
            else:
                self.current_batch.yolo_results = detected_objects.yolo_results
                self.current_batch.ages = detected_objects.ages
                self.current_batch.genders = detected_objects.genders
                self.current_batch.gender_scores = detected_objects.gender_scores

            # Draw detections if requested
            if self.draw and detected_objects.yolo_results is not None:
                out_im = detected_objects.plot(
                    img=out_im,
                    ages=True,
                    labels=True,
                    genders=False
                )
            else:
                print("WARNING: No valid detections found in the image")

            return detected_objects, out_im
