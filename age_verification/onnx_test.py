from typing import List, Optional, <PERSON><PERSON>
import onnxruntime
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import torchvision.transforms as transforms
import torch
import time
import os
import argparse
import logging
import cv2  # For letterbox and drawing

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default ImageNet normalization for Mivolo
IMAGE_MEAN = [0.485, 0.456, 0.406]
IMAGE_STD = [0.229, 0.224, 0.255]


# pre-processing for yolo face
def preprocess_yolo_input(
        image_pil: Image.Image,
        yolo_input_size: tuple = (640, 640)) -> Tuple[np.ndarray,float, Tuple[int, int]]:
    """
    prepares an image for YOLO ONNX model input (letterboxing, BGR, CHW, normalization)
    Args:
        image_pill(PIL.Image.Image): Input PIL image RGB.
        yolo_input_size (tuple): Target input size (width, height) for YOL<PERSON>.
    Returns:
        Tuple containing:
            - np.ndarray: preprocessed image as a Numpy array (1, C, H, W), float32.
            - float: Scale ratio used for letterboxing.
            - Tuple[int, int]: Padding (dw, dh) added during letterboxing.
    """
    img_w, img_h = image_pil.size
    w, h = yolo_input_size

    # calculate scale ratio and new_shape
    r = min(h / img_h, w / img_w)
    new_unpad_h, new_unpad_w = int(round(img_h * r)), int(round(img_w * r))

    # Convert pil to opencv format RGB numpy array
    img_cv = np.array(image_pil)  # HWC, RGB

    # resize if necessary
    if (img_h, img_w) != (new_unpad_h, new_unpad_w):
        img_resized_cv = cv2.resize(img_cv, (new_unpad_w, new_unpad_h), interpolation=cv2.INTER_LINEAR)
    else:
        img_resized_cv = img_cv
    
    # calculate padding
    dh, dw = h - new_unpad_h, w - new_unpad_w
    dh /= 2
    dw /= 2

    # Add letterbox padding
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))

    img_padded_cv = cv2.copyMakeBorder(img_resized_cv, top, bottom, left, right, cv2.BORDER_CONSTANT,
                                       value=(114, 114, 114))
    
    # convert to HWC, RGB, uint8 -> CHW, BGR, float32, normalized (0-1)
    # YOLO often expects BGR input
    img_bgr_chw = img_padded_cv.transpose((2, 0, 1))[::-1]  # HWC_RGB -> CHW_RGB -> CHW_BGR
    img_bgr_chw = np.ascontiguousarray(img_bgr_chw)
    img_normalized = img_bgr_chw.astype(np.float32) / 255.0

    # Add batch dimension
    img_batch = np.expand_dims(img_normalized, axis=0)  # 1, c, h, w

    return img_batch, r, (dw, dh)  # Return scale and padding for coordinate adjustment

# In onnx_test.py

def xywh2xyxy(x_np: np.ndarray) -> np.ndarray:
    """Convert NxD boxes from [x_center, y_center, width, height] to [x1, y1, x2, y2]"""
    y_np = np.copy(x_np)
    y_np[..., 0] = x_np[..., 0] - x_np[..., 2] / 2  # top left x
    y_np[..., 1] = x_np[..., 1] - x_np[..., 3] / 2  # top left y
    y_np[..., 2] = x_np[..., 0] + x_np[..., 2] / 2  # bottom right x
    y_np[..., 3] = x_np[..., 1] + x_np[..., 3] / 2  # bottom right y
    return y_np


def non_max_suppression_numpy(boxes_xyxy: np.ndarray, scores: np.ndarray, iou_threshold: float) -> List[int]:
    # ... (NMS implementation from Response #37, ensure it's available) ...
    if not boxes_xyxy.any():
        return []
    idxs = scores.argsort()[::-1]
    area = (boxes_xyxy[:, 2] - boxes_xyxy[:, 0]) * (boxes_xyxy[:, 3] - boxes_xyxy[:, 1])
    keep = []
    while idxs.size > 0:
        i = idxs[0]
        keep.append(i)
        x1_int = np.maximum(boxes_xyxy[i, 0], boxes_xyxy[idxs[1:], 0])
        y1_int = np.maximum(boxes_xyxy[i, 1], boxes_xyxy[idxs[1:], 1])
        x2_int = np.minimum(boxes_xyxy[i, 2], boxes_xyxy[idxs[1:], 2])
        y2_int = np.minimum(boxes_xyxy[i, 3], boxes_xyxy[idxs[1:], 3])
        w_int = np.maximum(0, x2_int - x1_int)
        h_int = np.maximum(0, y2_int - y1_int)
        inter_area = w_int * h_int
        iou = inter_area / (area[i] + area[idxs[1:]] - inter_area)
        idxs_to_keep = np.where(iou <= iou_threshold)[0]
        idxs = idxs[idxs_to_keep + 1]
    return keep


def postprocess_yolo_output(
    detections_raw: np.ndarray, # Expected (1, 6, 8400) from your log
    original_pil_image: Image.Image,
    scale_ratio: float,
    pad_dw: float,
    pad_dh: float,
    conf_threshold: float = 0.25,
    iou_threshold: float = 0.45,
    target_class_id: int = 1  # 1 for face, 0 for person (as per your latest info)
) -> List[Tuple[int, int, int, int]]:

    logger.info(f"YOLO Postprocessing: Raw output shape: {detections_raw.shape}")

    if detections_raw.ndim == 3 and detections_raw.shape[0] == 1:
        proposals_raw_dim_first = detections_raw[0] # Shape (6, 8400)
    elif detections_raw.ndim == 2 and detections_raw.shape[0] == 6 : # Already (6, 8400)
        proposals_raw_dim_first = detections_raw
    else:
        logger.error(f"Unexpected YOLO output shape after potential squeeze: {detections_raw.shape}. Expected (6, N) or (1,6,N)")
        return []

    # Transpose to (N_proposals, D_params_per_proposal) -> (8400, 6)
    proposals = proposals_raw_dim_first.transpose(1, 0)
    logger.info(f"YOLO Postprocessing: Proposals shape (N, D): {proposals.shape}")

    if proposals.shape[0] == 0 or proposals.shape[1] != 6:
        logger.error(f"Proposals have unexpected shape {proposals.shape} after transpose. Expected (N, 6).")
        return []

    # Assuming the 6 columns are: [cx, cy, w, h, person_score, face_score]
    # This is an assumption based on having 2 classes and 6 columns total.
    # If it were [cx,cy,w,h, objectness, class_id_integer], the logic would be different.
    
    boxes_xywh = proposals[:, :4]  # Center_x, Center_y, Width, Height
    person_scores = proposals[:, 4] # Score for class 0 (person)
    face_scores = proposals[:, 5]   # Score for class 1 (face)

    # Select scores based on the target_class_id
    if target_class_id == 1:  # Face
        relevant_scores = face_scores
    elif target_class_id == 0:  # Person
        relevant_scores = person_scores
    else:
        logger.error(f"Invalid target_class_id: {target_class_id}. Must be 0 (person) or 1 (face).")
        return []

    # Filter by confidence threshold
    conf_mask = relevant_scores >= conf_threshold
    if not np.any(conf_mask):
        logger.info(f"No detections for class_id {target_class_id} above confidence threshold {conf_threshold}.")
        return []

    boxes_to_nms_xywh = boxes_xywh[conf_mask]
    scores_to_nms = relevant_scores[conf_mask]
    
    logger.info(f"Detections before NMS (for target class {target_class_id}): {len(scores_to_nms)}")
    if not boxes_to_nms_xywh.any():
        return []

    # Convert boxes from [cx, cy, w, h] to [x1, y1, x2, y2]
    boxes_to_nms_xyxy = xywh2xyxy(boxes_to_nms_xywh)

    # Apply Non-Maximum Suppression
    keep_indices = non_max_suppression_numpy(boxes_to_nms_xyxy, scores_to_nms, iou_threshold)
    
    if not keep_indices:
        logger.info("No detections after NMS.")
        return []
            
    final_boxes_letterbox = boxes_to_nms_xyxy[keep_indices]
    logger.info(f"Detections after NMS for class {target_class_id}: {len(final_boxes_letterbox)}")

    # Scale boxes back to original image dimensions
    img_w_orig, img_h_orig = original_pil_image.size
    output_target_class_boxes = []
    for box_lb in final_boxes_letterbox:
        x1_lb, y1_lb, x2_lb, y2_lb = box_lb
        x1_orig = (x1_lb - pad_dw) / scale_ratio
        y1_orig = (y1_lb - pad_dh) / scale_ratio
        x2_orig = (x2_lb - pad_dw) / scale_ratio
        y2_orig = (y2_lb - pad_dh) / scale_ratio

        x1_orig = np.clip(x1_orig, 0, img_w_orig)
        y1_orig = np.clip(y1_orig, 0, img_h_orig)
        x2_orig = np.clip(x2_orig, 0, img_w_orig)
        y2_orig = np.clip(y2_orig, 0, img_h_orig)
        
        # Ensure valid box after scaling (width/height >= 1 pixel)
        if x1_orig < x2_orig - 1 and y1_orig < y2_orig - 1:
            output_target_class_boxes.append((int(round(x1_orig)), int(round(y1_orig)),
                                              int(round(x2_orig)), int(round(y2_orig))))
    
    logger.info(f"Final scaled boxes count for class {target_class_id}: {len(output_target_class_boxes)}")
    return output_target_class_boxes


# pre and post processing for mivolo age/gender model
def preprocess_mivolo_input(face_crop_pil: Image.Image, mivolo_input_size: tuple = (224, 224)) -> np.ndarray:
    """Prepares a face crop PIL image for mivolo onnx input."""
    transform_resize = transforms.Resize(mivolo_input_size)
    img = transform_resize(face_crop_pil)
    transform_to_tensor = transforms.ToTensor()
    img_tensor = transform_to_tensor(img)
    transform_normalize = transforms.Normalize(mean=IMAGE_MEAN, std=IMAGE_STD)
    img_normalized_tensor = transform_normalize(img_tensor)
    img_np = img_normalized_tensor.unsqueeze(0).cpu().numpy().astype(np.float32)
    return img_np


def postprocess_mivolo_output(logits: np.ndarray, age_threshold: int = 30) -> dict:
    """process mivolo logits"""
    results = {}
    if logits.shape != (1, 4):
        logger.warning(f"Mivolo: Unexpected logits shape: {logits.shape}. Expected (1, 4)")
        return {"error": "Unexpected mivolo output shape."}
    
    gender_logits = logits[0, :2]
    gender_probs = torch.softmax(torch.from_numpy(gender_logits), dim=0).numpy()
    gender_pred_class = np.argmax(gender_probs)
    gender_label = "Male" if gender_pred_class == 0 else "Female"
    results["gender"] = {"label": gender_label, "confidence": float(gender_probs[gender_pred_class])}

    age_logits = logits[0, 2:4]
    age_probs = torch.softmax(torch.from_numpy(age_logits), dim=0).numpy()
    age_pred_class = np.argmax(age_probs)
    age_label = "Child" if age_pred_class == 0 else "Adult"
    results["age"] = {"label": age_label, "confidence": float(age_probs[age_pred_class])}
    return results


def run_inference(
    full_image_path: str,
    yolo_onnx_path: str,
    mivolo_onnx_path: str,
    yolo_input_size_wh: tuple = (224, 224),  # W, H for yolo
    mivolo_input_size_sq: int = 224,
    face_class_id: int = 1,
    yolo_conf_thresh: float = 0.04,
    age_threshold: int = 30,
    provider: str = "CPUExecutionProvider",
    output_image_path: Optional[str] = None
):
    if not os.path.exists(full_image_path):
        logger.error(f"Full image not found: {full_image_path}")
        return
    if not os.path.exists(yolo_onnx_path):
        logger.error(f"YOLO onnx model not found: {yolo_onnx_path}")
        return
    if not os.path.exists(mivolo_onnx_path):
        logger.error(f"MiVOLO ONNX model not found: {yolo_onnx_path}")
    
    # Load og full image wit pil
    try:
        original_pil_img = Image.open(full_image_path).convert("RGB")
    except Exception as e:
        logger.error(f"Error opening full image {full_image_path}: {e}")
        return
    
    # Face detection
    logger.info("Running face detection....")
    yolo_input_np, scale_ratio, (pad_dw, pad_dh) = preprocess_yolo_input(original_pil_img, yolo_input_size_wh)

    available_providers = onnxruntime.get_available_providers()
    if provider == 'CUDAExecutionProvider' and 'CUDAExecutionProvider' not in available_providers:
        logger.warning("CUDAExecutionProvider requested but not available. Falling back to CPU for YOLO")
        yolo_provider = 'CPUExecutionProvider'
    else:
        yolo_provider = provider
    
    try:
        yolo_session = onnxruntime.InferenceSession(yolo_onnx_path, providers=[yolo_provider])
    except Exception as e:
        logger.error(f"Error creating yolo onnx session: {e}")
        return
    
    yolo_input_name = yolo_session.get_inputs()[0].name
    yolo_ort_inputs = {yolo_input_name: yolo_input_np}

    # print(yolo_ort_inputs)

    try:
        yolo_detections_raw = yolo_session.run(None, yolo_ort_inputs)[0]  # Usually first output
    except Exception as e:
        logger.error(f"Error during Yolo onnx inference: {e}")
        return
    
    print("yolo detections raw shape", yolo_detections_raw.shape)
    
    detected_face_boxes = postprocess_yolo_output(
        yolo_detections_raw, original_pil_img, scale_ratio, pad_dw, pad_dh,
        conf_threshold=yolo_conf_thresh, target_class_id=face_class_id
    )
    print(detected_face_boxes)
    logger.info(f"Detected {len(detected_face_boxes)} faces.")
    if not detected_face_boxes:
        logger.info("No faces detected by YOLO model.")
        if output_image_path:  # save the og image if no detections and output path provided
            original_pil_img.save(output_image_path)
            logger.info(f"Saved original no detections to {output_image_path}")
        return
    
    # Age gender Classification for each detected face
    logger.info("\n Running MiVOLO on detected faces...")
    if provider == 'CUDAExecutionProvider' and 'CUDAExecutionProvider' not in available_providers:
        logger.warning("CUDAExecutionProvider requested but not available. Falling back to CPU for YOLO")
        mivolo_provider = 'CPUExecutionProvider'
    else:
        mivolo_provider = provider
    
    try:
        mivolo_session = onnxruntime.InferenceSession(mivolo_onnx_path, providers=[mivolo_provider])
    except Exception as e:
        logger.error(f"Error creating mivolo onnx session: {e}")
        return
    
    mivolo_input_name = mivolo_session.get_inputs()[0].name

    # prepare for drawing
    draw_img = original_pil_img.copy()
    draw = ImageDraw.Draw(draw_img)
    try:
        font = ImageFont.truetype("arial.ttf", 15)
    except IOError:
        font = ImageFont.load_default()
    
    all_predictions = []

    for i, box in enumerate(detected_face_boxes):
        x1, y1, x2, y2 = box
        face_crop_pil = original_pil_img.crop((x1, y1, x2, y2))

        if face_crop_pil.width < 2 or face_crop_pil.height < 2:
            logger.warning(f"Skipping face crop {i} due to very small size: {face_crop_pil.size}")
            continue

        mivolo_input_np = preprocess_mivolo_input(face_crop_pil, mivolo_input_size=(mivolo_input_size_sq, mivolo_input_size_sq))
        mivolo_ort_inputs = {mivolo_input_name: mivolo_input_np}

        try:
            mivolo_logits = mivolo_session.run(None, mivolo_ort_inputs)[0]
        except Exception as e:
            logger.error(f"Error during mivolo onnx inference for face {i}: {e}")
            continue

        predictions = postprocess_mivolo_output(mivolo_logits, age_threshold)
        all_predictions.append({"box": box, "predictions": predictions})

        logger.info(f"  Face {i} @ {box}:")
        logger.info(f"   Age: {predictions.get('age', {}).get('label', 'N/A')}"
                    f"(Conf: {predictions.get('age', {}).get('confidence', 0):.2f})")
        logger.info(f" Gender: {predictions.get('gender', {}).get('label', 'N/A')}"
                    f"(Conf: {predictions.get('gender', {}).get('confidence', 0):.2f})")
        
        # Draw on image
        label = f"{predictions.get('age', {}).get('label', '')} {predictions.get('gender', {}).get('label', '')}"
        color = "green"
        if "Child" in predictions.get('age', {}).get('label', ''):
            color = "red"
        
        draw.rectangle([x1, y1, x2, y2], outline=color, width=2)
        text_y = y1 - 15 if y1 - 15 > 0 else y1 + 5  # position above or below box
        draw.text((x1, text_y), label, fill=color, font=font)
    
    if output_image_path:
        draw_img.save(output_image_path)
        logger.info(f"Annotated image saved to {output_image_path}")
    else:
        draw_img.show()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Two-Stage ONNX Inference: Face Detection -> Age/Gender Classification")
    parser.add_argument("--image", type=str, default="testing/testing_images/kid.jpeg", help="Path to the full input image file.")
    parser.add_argument("--yolo_onnx", type=str, default="models/detect_face.onnx", help="Path to the YOLO ONNX face detector model.")
    parser.add_argument("--mivolo_onnx", type=str, default="models/age_verification.onnx", help="Path to the MiVOLO ONNX age/gender model.")
    
    parser.add_argument("--yolo_input_size", type=int, default=640, help="Input size (square) for YOLO detector (e.g., 640 for 640x640).")
    parser.add_argument("--mivolo_input_size", type=int, default=224, help="Input size (square) for MiVOLO classifier.")
    parser.add_argument("--yolo_conf_thresh", type=float, default=0.40, help="Confidence threshold for YOLO face detections.")
    parser.add_argument("--face_class_id", type=int, default=1, help="Class ID for 'face' in the YOLO model's output.")
    parser.add_argument("--age_threshold", type=int, default=30, help="Age threshold for child/adult classification.")
    parser.add_argument("--provider", type=str, default="CPUExecutionProvider", choices=['CPUExecutionProvider', 'CUDAExecutionProvider'], help="ONNX Runtime execution provider.")
    parser.add_argument("--output_image", type=str, default="testing/onnx_output.jpg", help="Path to save the annotated output image. If None, displays the image.")
    
    args = parser.parse_args()

    run_inference(
        full_image_path=args.image,
        yolo_onnx_path=args.yolo_onnx,
        mivolo_onnx_path=args.mivolo_onnx,
        yolo_input_size_wh=(args.yolo_input_size, args.yolo_input_size),
        mivolo_input_size_sq=args.mivolo_input_size,
        face_class_id=args.face_class_id,
        yolo_conf_thresh=args.yolo_conf_thresh,
        age_threshold=args.age_threshold,
        provider=args.provider,
        output_image_path=args.output_image
    )
