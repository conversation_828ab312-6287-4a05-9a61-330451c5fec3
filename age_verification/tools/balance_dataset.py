import json
from collections import defaultdict
import random
from tqdm import tqdm


def balance_dataset(input_json_path, output_json_path, samples_per_class=None, seed=42):
    """
    Balance the dataset by ensuring equal number of examples for each class
    """
    print(f"Loading dataset from {input_json_path}...")
    with open(input_json_path, "r") as f:
        data = json.load(f)

    print(f"Total samples in dataset: {len(data)}")

    # group samples by class
    class_samples = defaultdict(list)
    for sample in data:
        age_label = sample["ages"][0]
        if age_label < 30:
            age_label = 0  # Child
        else:
            age_label = 1
        class_samples[age_label].append(sample)

    # print class distribution
    print("\nClass distribution before balancing")
    for label, samples in class_samples.items():
        print(f"Class {label}: {len(samples)} samples")

    # find the minimum number of samples across all classes if samples per class is not specified
    if samples_per_class is None:
        samples_per_class = min(len(samples) for samples in class_samples.values())

    print(f"\nBalancing to {samples_per_class} samples per class")

    # Randomly sample from each class to get equal number of samples
    balanced_data = []
    random.seed(seed)

    for label, samples in tqdm(class_samples.items(), desc="Balancing classes"):
        if len(samples) > samples_per_class:
            # randomly select samples_per_class from this class
            selected_samples = random.sample(samples, samples_per_class)
        else:
            # Use all samples from this class
            selected_samples = samples

        balanced_data.extend(selected_samples)

    # shuffle the balanced dataset
    random.shuffle(balanced_data)

    # print the distribution after processing
    balance_class_samples = defaultdict(list)
    for sample in balanced_data:
        age_label = sample["ages"][0]
        if age_label < 30:
            age_label = 0  # Child
        else:
            age_label = 1

        balance_class_samples[age_label].append(sample)

    print("\nClass distribution after balancing")
    for label, samples in balance_class_samples.items():
        print(f"Class {label}: {len(samples)} samples")

    # save the balanced dataset
    print(f"\nSaving balanced dataset to {output_json_path}...")
    with open(output_json_path, "w") as f:
        json.dump(balanced_data, f, indent=2)

    print(f"Balanced dataset saved with {len(balanced_data)} samples")


def main():
    balance_dataset(
        input_json_path="/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/imdb_clean/annotations.json",
        output_json_path="/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/imdb_clean/balanced_annotations.json",
        seed=42)


if __name__ == "__main__":
    main()
