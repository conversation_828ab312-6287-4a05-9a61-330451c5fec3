import json
import argparse
from pathlib import Path
import logging
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def combine_annotations(json_files: List[str], output_file: str, validate_schema: bool = True):
    """
    Combines multiple annotation JSON files into a single JSON file,
    resolving image paths to absolute paths.

    Args:
        json_files (List[str]): A list of paths to the input JSON files.
        output_file (str): Path to save the combined JSON file.
        validate_schema (bool): If True, performs a basic check on the schema of each sample.
    """
    combined_data: List[Dict[str, Any]] = []
    # Now, processed_image_paths will store absolute paths to check for duplicates
    processed_absolute_image_paths = set()

    logging.info(f"Starting to combine {len(json_files)} JSON files.")

    for json_path_str in json_files:
        json_path_obj = Path(json_path_str).resolve() # Resolve to absolute path first

        if not json_path_obj.is_file():
            logging.warning(f"Input JSON file not found: {json_path_obj}. Skipping.")
            continue

        # The base directory for relative image paths in this specific JSON file
        # is the directory containing the JSON file itself.
        base_dir_for_this_json = json_path_obj.parent

        try:
            logging.info(f"Processing file: {json_path_obj} (Base image dir: {base_dir_for_this_json})")
            with open(json_path_obj, 'r') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                logging.warning(f"File {json_path_obj} does not contain a list of annotations. Skipping.")
                continue

            added_from_this_file = 0
            for i, sample in enumerate(data):
                if not isinstance(sample, dict):
                    logging.warning(f"Sample {i} in {json_path_obj} is not a dictionary. Skipping sample.")
                    continue

                # Basic schema validation
                if validate_schema:
                    required_keys = {"image_path", "split", "ages", "genders", "face_boxes"}
                    if not required_keys.issubset(sample.keys()):
                        logging.warning(f"Sample {i} in {json_path_obj} (image: {sample.get('image_path', 'N/A')}) "
                                        f"is missing one or more required keys: {required_keys - sample.keys()}. Skipping sample.")
                        continue
                
                relative_image_path_in_sample = sample.get("image_path")
                if not relative_image_path_in_sample:
                    logging.warning(f"Sample {i} in {json_path_obj} has no 'image_path'. Skipping sample.")
                    continue

                # --- Convert relative image_path to absolute path ---
                # Path(relative_image_path_in_sample).is_absolute() could be used if some paths are already absolute
                # Forcing resolution from base_dir_for_this_json for consistency:
                absolute_image_path = (base_dir_for_this_json / relative_image_path_in_sample).resolve()

                if not absolute_image_path.is_file():
                    logging.warning(f"Image file not found at resolved absolute path: {absolute_image_path} "
                                    f"(original relative: '{relative_image_path_in_sample}' from {json_path_obj}). Skipping sample.")
                    continue
                
                # Use absolute path string for duplicate checking and storage
                abs_path_str = str(absolute_image_path)

                if abs_path_str in processed_absolute_image_paths:
                    logging.debug(f"Duplicate absolute image_path '{abs_path_str}' found. Skipping.")
                    continue
                
                # Create a new sample dictionary or update the existing one
                # to ensure we don't modify the original 'sample' if it's reused (though json.load creates new objects)
                new_sample = sample.copy() # Make a copy to modify
                new_sample["image_path"] = abs_path_str # Update to absolute path

                combined_data.append(new_sample)
                processed_absolute_image_paths.add(abs_path_str)
                added_from_this_file += 1
            
            logging.info(f"Added {added_from_this_file} samples from {json_path_obj}. Total unique samples so far: {len(combined_data)}")

        except json.JSONDecodeError:
            logging.error(f"Error decoding JSON from file: {json_path_obj}. Skipping.")
        except Exception as e:
            logging.error(f"An unexpected error occurred while processing {json_path_obj}: {e}. Skipping.")

    if not combined_data:
        logging.warning("No data was combined. Output file will not be created.")
        return

    output_path_obj = Path(output_file)
    try:
        output_path_obj.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path_obj, 'w') as f:
            json.dump(combined_data, f, indent=2)
        logging.info(f"Successfully combined {len(combined_data)} unique samples into {output_path_obj}")
    except IOError as e:
        logging.error(f"Failed to write combined JSON to {output_path_obj}: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred while writing the output file: {e}")

def main():
    parser = argparse.ArgumentParser(description="Combine multiple annotation JSON files, converting image paths to absolute.")
    parser.add_argument(
        "input_files",
        nargs='+',
        help="Paths to the input JSON annotation files (e.g., file1.json file2.json ...)."
    )
    parser.add_argument(
        "-o", "--output_file",
        required=True,
        help="Path to the output combined JSON file."
    )
    parser.add_argument(
        "--no_validate",
        action="store_false",
        dest="validate_schema",
        help="Disable basic schema validation for each sample."
    )
    parser.set_defaults(validate_schema=True)

    args = parser.parse_args()

    combine_annotations(args.input_files, args.output_file, args.validate_schema)

if __name__ == "__main__":
    main()