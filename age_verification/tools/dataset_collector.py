import logging
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from tqdm import tqdm
import cv2
import multiprocessing as mp
from functools import partial
import numpy as np
import pyarrow.parquet as pq
import warnings


class DatasetCollector:
    def __init__(self, dataset_dir: Path, num_workers: int = None, batch_size: int = 100, debug: bool = False):
        self.dataset_dir = dataset_dir
        self.num_workers = num_workers or max(1, mp.cpu_count() - 1)  # Leave one CPU free
        self.batch_size = batch_size
        self.debug = debug
        self.dataset_configs = {
            "face_dataset": {
                "image_root": "images",
                "csv_name": "annotations.csv"
            },
            "utk_face": {
                "image_root": "images",
                "csv_name": "annotations.csv"
            },
            "knn_age": {
                "image_root": "images",
                "csv_name": "annotations.csv"
            },
            "lagenda": {
                "image_root": "images",
                "csv_name": "lagenda_annotation.csv"
            },
            "imdb_clean": {
                "image_root": None,
                "csv_name": "imdb_annotation/original_annotations/full_original_annotations.csv"
            },
            "hugging_face_age": {
                "data_dir": "data",
                "parquet_pattern": "*.parquet"
            }
        }

    def log_debug(self, message: str):
        """Log message only if debug is enabled."""
        if self.debug:
            logging.info(message)

    def validate_age(self, age_val: Any) -> int:
        """Validate and convert age value to int."""
        try:
            age = int(float(age_val))
            return age if 0 <= age <= 110 else -1
        except (ValueError, TypeError):
            return -1

    def validate_gender(self, gender_val: Any) -> int:
        """Validate and convert gender value to int."""
        if pd.isna(gender_val) or gender_val == -1 or str(gender_val) == "-1":
            return -1

        if isinstance(gender_val, (int, float)):
            return int(gender_val) if gender_val in [0, 1] else -1

        gender_str = str(gender_val).upper()
        if gender_str == 'M':
            return 0
        elif gender_str == 'F':
            return 1
        return -1

    def validate_bbox(
        self,
        bbox_vals: Tuple[Any, Any, Any, Any],
        image_shape: Optional[Tuple[int, int]] = None,
        min_size: int = 32
    ) -> Optional[Tuple[int, int, int, int]]:
        """Validate and convert bbox coordinates."""
        try:
            if any(pd.isna(v) or v == -1 or v == "-1" for v in bbox_vals):
                return None

            x0, y0, x1, y1 = map(lambda x: int(float(x)), bbox_vals)
            
            # Basic validation
            if x0 >= 0 and y0 >= 0 and x1 > x0 and y1 > y0:
                # If image shape is provided, validate against image dimensions
                if image_shape is not None:
                    img_h, img_w = image_shape
                    # Normalize coordinates to be within image bounds
                    x0 = max(0, min(x0, img_w - 1))
                    y0 = max(0, min(y0, img_h - 1))
                    x1 = max(0, min(x1, img_w - 1))
                    y1 = max(0, min(y1, img_h - 1))
                    
                    # Check if box is still valid after normalization
                    if x1 <= x0 or y1 <= y0:
                        return None
                        
                # Check minimum size
                width = x1 - x0
                height = y1 - y0
                if width >= min_size and height >= min_size:
                    return (x0, y0, x1, y1)
        except (ValueError, TypeError):
            pass
        return None

    def create_sample(
        self,
        image_path: Path,
        age: int,
        gender: int = -1,
        bbox: Optional[Tuple[int, int, int, int]] = None,
        relative_path: Optional[Path] = None
    ) -> Dict[str, Any]:
        """Create a standardized sample dictionary."""
        resolved_path_str = ""
        path_resolved_flag = False
        resolve_error_msg = None

        try:
            # logging.debug(f"Attempting to resolve: {image_path}")
            resolved_path = image_path.resolve(strict=True)  # strict=True will raise FileNotFoundError if not found
            resolved_path_str = str(resolved_path)
            path_resolved_flag = True
            # logging.debug(f"Successfully resolved to: {resolved_path_str}")
        except FileNotFoundError:
            logging.warning(f"File not found during resolve: {image_path}")
            resolved_path_str = str(image_path)  # Keep original path string
            resolve_error_msg = "FileNotFoundError"
        except Exception as e:
            logging.error(f"Error resolving path {image_path}: {e}")
            resolved_path_str = str(image_path)  # Keep original path string
            resolve_error_msg = str(e)

        sample_dict = {
            'image_path': resolved_path_str,  # Store as string, as Path objects might not be JSON serializable directly everywhere
            'age': age,
            'gender': gender,
            'bbox': list(bbox) if bbox is not None else None,
            'relative_path_from_source': str(relative_path or Path(image_path.name)),  # Store as string
            'path_resolved': path_resolved_flag
        }
        if resolve_error_msg:
            sample_dict['resolve_error'] = resolve_error_msg
        return sample_dict

    def process_image_batch(self, image_paths: List[Path], dataset_type: str) -> List[Dict[str, Any]]:
        """Process a batch of images in parallel."""
        samples = []
        for image_path in image_paths:
            try:
                if dataset_type == "knn_age":
                    # Process KNN Age image
                    filename_stem = image_path.stem
                    parts = filename_stem.split('_')

                    if len(parts) < 2:  # Need at least ID_AGE
                        if self.dataset_dir.joinpath(image_path).is_file():  # Check if it's a file in the search scope
                            logging.debug(f"Skipping {image_path}: Filename format incorrect (expected ID_AGE.jpg).")
                        continue

                    # Parse age
                    age_str = parts[1]
                    if not age_str.isdigit():
                        if self.dataset_dir.joinpath(image_path).is_file():  # Check if it's a file in the search scope
                            logging.debug(f"Skipping {image_path}: Age part is not a digit ('{age_str}').")
                        continue

                    age = self.validate_age(age_str)
                    if age == -1:
                        if self.dataset_dir.joinpath(image_path).is_file():  # Check if it's a file in the search scope
                            logging.debug(f"Skipping {image_path}: Invalid age value after validation ('{age_str}').")
                        continue

                    # Validate image
                    try:
                        img = cv2.imread(str(image_path))
                        if img is None:
                            if self.dataset_dir.joinpath(image_path).is_file():  # Check if it's a file in the search scope
                                logging.debug(
                                    f"Skipping {image_path}: Could not read image file with OpenCV."
                                )
                            continue
                        
                        # For KNN Age, we use the full image as it's already cropped
                        img_h, img_w = img.shape[:2]
                        
                    except Exception:
                        continue

                    try:
                        # Preserve the full directory structure in relative path
                        relative_path = image_path.relative_to(self.dataset_dir)
                    except ValueError:
                        relative_path = Path(image_path.name)

                    # For KNN Age, we don't need bbox as images are already cropped
                    # Gender is not available in KNN Age dataset, so set to -1
                    samples.append(self.create_sample(
                        image_path=image_path,
                        age=age,
                        gender=-1,  # Explicitly set gender to -1 as it's not available
                        bbox=None,  # No bbox needed as images are pre-cropped
                        relative_path=relative_path
                    ))

            except Exception as e:
                logging.error(f"Error processing {image_path}: {e}")
                continue

        return samples

    def collect_samples_knn_age(self, max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect samples from KNN Age dataset using batch processing.
        Note: KNN Age images are already face-cropped, so no face detection is needed.
        Gender information is not available in this dataset, so it's set to -1."""
        samples: List[Dict[str, Any]] = []
        logging.info(f"Collecting samples from KNN Age in {self.dataset_dir}")

        # List all image files recursively from the dataset directory
        image_files = []
        for ext in ["*.jpg", "*.png", "*.jpeg"]:
            image_files.extend(list(self.dataset_dir.rglob(ext)))
        
        logging.info(f"Found {len(image_files)} image files (showing first 5):")
        for img in image_files[:5]:
            logging.info(f"  {img.relative_to(self.dataset_dir)}")

        if max_samples is not None and max_samples > 0:
            image_files = image_files[:max_samples]
            logging.info(f"Limited to first {max_samples} images")

        # Split files into batches using the configured batch size
        batches = [image_files[i:i + self.batch_size] for i in range(0, len(image_files), self.batch_size)]
        logging.info(f"Split {len(image_files)} images into {len(batches)} batches of size {self.batch_size}")

        # Process batches in parallel
        with mp.Pool(processes=self.num_workers) as pool:
            process_func = partial(self.process_image_batch, dataset_type="knn_age")
            results = list(tqdm(
                pool.imap(process_func, batches),
                total=len(batches),
                desc="Processing image batches"
            ))

        # Combine results from all batches
        for batch_samples in results:
            samples.extend(batch_samples)

        logging.info(f"Found {len(samples)} valid samples from KNN_AGE.")
        return samples

    def collect_samples_imdb_clean(self, max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect samples from IMDB-clean dataset."""
        csv_path = self.dataset_configs["imdb_clean"]["csv_name"]
        full_csv_path = self.dataset_dir / csv_path

        logging.info("Checking dataset directory structure:")
        logging.info(f"Dataset root: {self.dataset_dir}")
        logging.info(f"Image base dir: {self.dataset_dir}")
        logging.info(f"CSV path: {full_csv_path}")

        # List some image files to verify directory structure
        image_dir = self.dataset_dir
        image_files = list(image_dir.glob("**/*.jpg"))
        logging.info(f"Found {len(image_files)} image files (showing first 5):")
        for img in image_files[:5]:
            logging.info(f"  {img.relative_to(self.dataset_dir)}")

        if not full_csv_path.exists():
            logging.error(f"CSV file not found: {full_csv_path}")
            return []

        try:
            df = pd.read_csv(full_csv_path)
            if max_samples is not None and max_samples > 0:
                df = df.head(max_samples)
                logging.info(f"Limited to first {max_samples} rows from {full_csv_path}")
            else:
                logging.info(f"Processing full CSV file: {full_csv_path}")

            logging.info("\nFirst few rows from CSV:")
            logging.info(df.head().to_string())

            # Define column mappings for different CSV formats
            column_mappings = {
                'format1': {
                    'img_name': 'img_name',
                    'face_x0': 'face_x0',
                    'face_y0': 'face_y0',
                    'face_x1': 'face_x1',
                    'face_y1': 'face_y1'
                },
                'format2': {
                    'img_name': 'filename',
                    'face_x0': 'x_min',
                    'face_y0': 'y_min',
                    'face_x1': 'x_max',
                    'face_y1': 'y_max'
                }
            }

            # Try to detect which format we're dealing with
            format_detected = None
            for fmt, mapping in column_mappings.items():
                if all(col in df.columns for col in mapping.values()):
                    format_detected = fmt
                    break

            if format_detected is None:
                logging.error("CSV format not recognized. Required columns not found.")
                return []

            # Map column names to standard format
            column_map = column_mappings[format_detected]
            df = df.rename(columns={v: k for k, v in column_map.items()})

            # Filter out rows with invalid values
            valid_rows = (
                (df['age'] >= 0) &  # Valid age
                (df['gender'].isin(['M', 'F'])) &  # Valid gender
                (df['face_x0'] >= 0) & (df['face_y0'] >= 0) &  # Valid face coordinates
                (df['face_x1'] > df['face_x0']) & (df['face_y1'] > df['face_y0'])
            )
            df = df[valid_rows]

            if df.empty:
                logging.error("No valid rows found in CSV after filtering")
                return []

            # Convert gender to numeric (M=0, F=1)
            df['gender'] = df['gender'].map({'M': 0, 'F': 1})

            # Process each row
            all_samples = []
            stats = {
                "invalid_age": 0,
                "invalid_bbox": 0,
                "valid_faces": 0,
                "image_not_found": 0
            }

            for _, row in tqdm(df.iterrows(), total=len(df), desc="Processing IMDB Clean rows"):
                img_name = row['img_name']
                age = int(row['age'])
                gender = row['gender']

                # Extract subdirectory from image name (e.g., "88" from "88/nm0810488_...")
                try:
                    subdir = img_name.split('/')[0]
                    if not subdir.isdigit():
                        continue
                except IndexError:
                    continue

                # Construct image path with imdb-clean-1024 prefix
                img_path = self.dataset_dir / "imdb-clean-1024" / subdir / img_name.split('/')[-1]
                if not img_path.exists():
                    stats["image_not_found"] += 1
                    continue

                # Get original image dimensions
                try:
                    img = cv2.imread(str(img_path))
                    if img is None:
                        continue
                    orig_h, orig_w = img.shape[:2]
                except Exception as e:
                    logging.warning(f"Could not read image dimensions for {img_path}: {e}")
                    continue

                # Get face coordinates from CSV
                face_bbox = [
                    int(row['face_x0']),
                    int(row['face_y0']),
                    int(row['face_x1']),
                    int(row['face_y1'])
                ]

                # Calculate scaling factors
                scale_x = 1024 / orig_w
                scale_y = 1024 / orig_h

                # Scale coordinates to 1024x1024
                scaled_bbox = [
                    int(face_bbox[0] * scale_x),
                    int(face_bbox[1] * scale_y),
                    int(face_bbox[2] * scale_x),
                    int(face_bbox[3] * scale_y)
                ]

                # Validate scaled bbox
                if not (scaled_bbox[0] < scaled_bbox[2] and scaled_bbox[1] < scaled_bbox[3]):
                    stats["invalid_bbox"] += 1
                    continue

                # Ensure coordinates are within 1024x1024 bounds
                scaled_bbox = [
                    max(0, min(scaled_bbox[0], 1023)),
                    max(0, min(scaled_bbox[1], 1023)),
                    max(0, min(scaled_bbox[2], 1023)),
                    max(0, min(scaled_bbox[3], 1023))
                ]

                stats["valid_faces"] += 1
                all_samples.append({
                    'image_path': str(img_path),
                    'age': age,
                    'gender': gender,
                    'bbox': [scaled_bbox],  # Wrap in list to match expected format
                    'path_resolved': True
                })

            logging.info("\nSample collection statistics:")
            for key, value in stats.items():
                logging.info(f"{key}: {value}")

            # Get unique images
            unique_images = set(sample['image_path'] for sample in all_samples)
            logging.info(f"Processing {len(unique_images)} unique images...")

            # Create final samples list
            final_samples = []
            for sample in tqdm(all_samples, desc="Creating samples"):
                final_samples.append(sample)

            logging.info(f"Collected {len(final_samples)} samples from IMDB Clean (with face annotations)")
            return final_samples

        except Exception as e:
            logging.error(f"Error processing IMDB-clean dataset: {e}")
            return []

    def process_lagenda_batch(self, batch_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of Lagenda data in parallel."""
        samples = []
        image_subdir = self.dataset_dir / "images"
        
        for row in batch_data:
            try:
                img_name_in_csv = row['img_name']
                image_basename = Path(img_name_in_csv).name

                full_image_path = image_subdir / image_basename
                relative_img_path_from_csv = Path(img_name_in_csv)

                if not full_image_path.is_file():
                    full_image_path = image_subdir / img_name_in_csv
                    if not full_image_path.is_file():
                        continue

                # Validate age
                age_val = row['age']
                age = self.validate_age(age_val)
                if age == -1:
                    continue

                # Validate gender
                gender_val = row['gender']
                gender = self.validate_gender(gender_val)
                if gender == -1:
                    continue

                # Validate bbox
                bbox_coords = self.validate_bbox((
                    row['face_x0'],
                    row['face_y0'],
                    row['face_x1'],
                    row['face_y1']
                ))
                if bbox_coords is None:
                    continue

                samples.append(self.create_sample(
                    image_path=full_image_path,
                    age=age,
                    gender=gender,
                    bbox=bbox_coords,
                    relative_path=relative_img_path_from_csv
                ))

            except Exception as e:
                logging.error(f"Error processing row: {e}")
                continue

        return samples

    def collect_samples_lagenda(self, max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect samples from Lagenda dataset using batch processing.
        Note: Lagenda dataset includes both age and gender information."""
        samples: List[Dict[str, Any]] = []
        logging.info(f"Collecting samples from lagenda in {self.dataset_dir}")

        image_subdir = self.dataset_dir / "images"
        csv_path = self.dataset_dir / "lagenda_annotation.csv"

        if not image_subdir.is_dir():
            logging.error(f"Image sub dir not found: {image_subdir}")
            return samples
        if not csv_path.is_file():
            logging.error(f"Failed to read or parse csv {csv_path}")
            return samples

        try:
            df = pd.read_csv(csv_path)
            if max_samples is not None and max_samples > 0:
                df = df.head(max_samples)
                logging.info(f"Limited to first {max_samples} rows from {csv_path}")
            else:
                logging.info(f"Read {len(df)} rows from {csv_path}")
        except Exception as e:
            logging.error(f"Failed to read or parse csv {csv_path}: {e}")
            return samples

        required_cols = ["img_name", "age", "gender", "face_x0", "face_y0", "face_x1", "face_y1"]
        if not all(col in df.columns for col in required_cols):
            logging.error(
                f"Missing required columns in {csv_path}. "
                f"Need {required_cols}, Found {df.columns.tolist()}"
            )
            return samples

        # Convert DataFrame to list of dictionaries with explicit type conversion
        data_list = []
        for _, row in df.iterrows():
            data_list.append({
                'img_name': str(row['img_name']),
                'age': float(row['age']),
                'gender': str(row['gender']),
                'face_x0': float(row['face_x0']),
                'face_y0': float(row['face_y0']),
                'face_x1': float(row['face_x1']),
                'face_y1': float(row['face_y1'])
            })
        
        # Split data into batches using the configured batch size
        batches = [data_list[i:i + self.batch_size] for i in range(0, len(data_list), self.batch_size)]
        logging.info(f"Split {len(data_list)} rows into {len(batches)} batches of size {self.batch_size}")

        # Process batches in parallel
        with mp.Pool(processes=self.num_workers) as pool:
            results = list(tqdm(
                pool.imap(self.process_lagenda_batch, batches),
                total=len(batches),
                desc="Processing data batches"
            ))

        # Combine results from all batches
        for batch_samples in results:
            samples.extend(batch_samples)

        logging.info(f"Found {len(samples)} valid samples from lagenda.")
        return samples

    def process_hugging_face_batch(self, batch_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of Hugging Face age data."""
        samples = []
        stats = {
            "total_processed": 0,
            "invalid_image_data": 0,
            "invalid_dimensions": 0,
            "invalid_age": 0,
            "invalid_gender": 0,
            "successful": 0,
            "errors": 0,
            "corrupted_images": 0,
            "format_conversion_errors": 0,
            "memory_errors": 0,
            "libpng_warnings": 0
        }
        
        # Suppress libpng warnings
        warnings.filterwarnings('ignore', category=UserWarning, module='PIL')
        
        for row in batch_data:
            stats["total_processed"] += 1
            try:
                # Validate input data structure
                if not isinstance(row, dict) or 'image' not in row or 'bytes' not in row['image']:
                    stats["invalid_image_data"] += 1
                    self.log_debug(f"Invalid data structure in row: {row.keys()}")
                    continue
                
                # Extract image bytes and convert to numpy array
                image_bytes = row['image']['bytes']
                if not isinstance(image_bytes, bytes):
                    stats["invalid_image_data"] += 1
                    self.log_debug(f"Invalid image bytes type: {type(image_bytes)}")
                    continue
                
                try:
                    # Use numpy's frombuffer for efficient memory usage
                    nparr = np.frombuffer(image_bytes, np.uint8)
                except Exception as e:
                    self.log_debug(f"Failed to convert image bytes to numpy array: {e}")
                    stats["memory_errors"] += 1
                    continue
                
                try:
                    # Suppress OpenCV warnings during image decoding
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        # Use IMREAD_IGNORE_ORIENTATION to handle EXIF orientation
                        # Use IMREAD_COLOR to ensure we get a 3-channel image
                        # Use IMREAD_UNCHANGED to preserve alpha channel if present
                        img = cv2.imdecode(
                            nparr,
                            cv2.IMREAD_IGNORE_ORIENTATION | cv2.IMREAD_COLOR | cv2.IMREAD_UNCHANGED
                        )
                except Exception as e:
                    self.log_debug(f"Failed to decode image: {e}")
                    stats["corrupted_images"] += 1
                    continue
                
                if img is None:
                    stats["corrupted_images"] += 1
                    self.log_debug("Failed to decode image: img is None")
                    continue
                
                # Handle different image formats
                try:
                    if len(img.shape) == 2:  # Grayscale
                        img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
                    elif len(img.shape) == 3:
                        if img.shape[2] == 4:  # RGBA
                            img = cv2.cvtColor(img, cv2.COLOR_RGBA2BGR)
                        elif img.shape[2] != 3:  # Not RGB/BGR
                            stats["format_conversion_errors"] += 1
                            self.log_debug(f"Unsupported number of channels: {img.shape[2]}")
                            continue
                    else:
                        stats["format_conversion_errors"] += 1
                        self.log_debug(f"Invalid image shape: {img.shape}")
                        continue
                except cv2.error as e:
                    stats["format_conversion_errors"] += 1
                    self.log_debug(f"OpenCV error during format conversion: {e}")
                    continue
                
                # Get image dimensions
                img_h, img_w = img.shape[:2]
                
                # Skip if image is too small or too large
                if img_h < 32 or img_w < 32 or img_h > 4096 or img_w > 4096:
                    stats["invalid_dimensions"] += 1
                    self.log_debug(f"Invalid image dimensions: {img_w}x{img_h}")
                    continue
                
                # Validate age
                age = self.validate_age(row.get('age'))
                if age == -1:
                    stats["invalid_age"] += 1
                    self.log_debug(f"Invalid age value: {row.get('age')}")
                    continue
                
                # Validate gender
                gender = self.validate_gender(row.get('gender'))
                if gender == -1:
                    stats["invalid_gender"] += 1
                    self.log_debug(f"Invalid gender value: {row.get('gender')}")
                    continue
                
                # For Hugging Face dataset, we use the full image as it's already cropped
                bbox = [0, 0, img_w, img_h]
                
                # Create a unique filename for the image using a hash of the image data
                image_hash = hash(image_bytes)
                image_filename = f"hf_age_{image_hash}_{age}_{gender}.jpg"
                image_path = self.dataset_dir / "images" / image_filename
                
                # Ensure the images directory exists
                try:
                    image_path.parent.mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    self.log_debug(f"Failed to create directory {image_path.parent}: {e}")
                    stats["errors"] += 1
                    continue
                
                # Save the image as JPEG with quality 95
                try:
                    # Convert BGR to RGB for processing
                    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    
                    # Apply basic image normalization and cleaning
                    img_rgb = img_rgb.astype(np.float32) / 255.0
                    
                    # Remove any NaN or Inf values
                    img_rgb = np.nan_to_num(img_rgb, nan=0.0, posinf=1.0, neginf=0.0)
                    
                    # Clip values to valid range
                    img_rgb = np.clip(img_rgb, 0, 1)
                    
                    # Convert back to uint8
                    img_rgb = (img_rgb * 255).astype(np.uint8)
                    
                    # Convert back to BGR for saving
                    img_bgr = cv2.cvtColor(img_rgb, cv2.COLOR_RGB2BGR)
                    
                    # Save with high quality and optimized settings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        success = cv2.imwrite(
                            str(image_path),
                            img_bgr,
                            [cv2.IMWRITE_JPEG_QUALITY, 95, cv2.IMWRITE_JPEG_OPTIMIZE, 1]
                        )
                    
                    if not success:
                        raise Exception("Failed to write image file")
                        
                except Exception as e:
                    self.log_debug(f"Failed to save image {image_path}: {e}")
                    stats["errors"] += 1
                    continue
                
                # Verify the saved image can be read back
                try:
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        saved_img = cv2.imread(str(image_path))
                        if saved_img is None:
                            raise Exception("Saved image could not be read back")
                        
                        # Verify image dimensions match
                        if saved_img.shape != img.shape:
                            raise Exception(f"Saved image dimensions mismatch: {saved_img.shape} vs {img.shape}")
                            
                except Exception as e:
                    self.log_debug(f"Failed to verify saved image {image_path}: {e}")
                    stats["errors"] += 1
                    continue
                
                samples.append(self.create_sample(
                    image_path=image_path,
                    age=age,
                    gender=gender,
                    bbox=bbox,
                    relative_path=Path(image_filename)
                ))
                stats["successful"] += 1
                
            except Exception as e:
                self.log_debug(f"Error processing Hugging Face sample: {e}")
                stats["errors"] += 1
                continue
        
        # Log batch statistics only in debug mode
        if self.debug:
            self.log_debug("Batch processing statistics:")
            for key, value in stats.items():
                self.log_debug(f"  {key}: {value}")
                
        return samples

    def collect_samples_hugging_face_age(self, max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect samples from Hugging Face age dataset."""
        samples: List[Dict[str, Any]] = []
        self.log_debug(f"Collecting samples from Hugging Face age dataset in {self.dataset_dir}")
        
        config = self.dataset_configs["hugging_face_age"]
        data_dir = self.dataset_dir / config["data_dir"]
        
        if not data_dir.is_dir():
            logging.error(f"Data directory not found: {data_dir}")
            return samples
            
        # Find all parquet files
        parquet_files = list(data_dir.glob(config["parquet_pattern"]))
        if not parquet_files:
            logging.error(f"No parquet files found in {data_dir}")
            return samples
            
        self.log_debug(f"Found {len(parquet_files)} parquet files")
        
        total_samples = 0
        total_processed = 0
        
        # Process each parquet file
        for parquet_file in tqdm(parquet_files, desc="Processing parquet files"):
            try:
                # Read parquet file
                table = pq.read_table(parquet_file)
                df = table.to_pandas()
                
                total_samples += len(df)
                
                if max_samples is not None and max_samples > 0:
                    if total_processed >= max_samples:
                        break
                    remaining = max_samples - total_processed
                    df = df.head(remaining)
                
                # Convert DataFrame to list of dictionaries
                data_list = df.to_dict('records')
                
                # Split data into batches using the configured batch size
                batches = [data_list[i:i + self.batch_size] for i in range(0, len(data_list), self.batch_size)]
                
                # Process batches in parallel
                with mp.Pool(processes=self.num_workers) as pool:
                    results = list(tqdm(
                        pool.imap(self.process_hugging_face_batch, batches),
                        total=len(batches),
                        desc=f"Processing batches from {parquet_file.name}"
                    ))
                
                # Combine results from all batches
                for batch_samples in results:
                    samples.extend(batch_samples)
                    total_processed += len(batch_samples)
                    
            except Exception as e:
                logging.error(f"Error processing parquet file {parquet_file}: {e}")
                continue
        
        # Log final statistics
        self.log_debug("\nFinal statistics:")
        self.log_debug(f"Total samples in dataset: {total_samples}")
        self.log_debug(f"Successfully processed: {total_processed}")
        self.log_debug(f"Final sample count: {len(samples)}")
        
        return samples

    def process_utk_face_batch(self, image_paths: List[Path]) -> List[Dict[str, Any]]:
        """Process a batch of UTK Face images in parallel."""
        samples = []
        for image_path in image_paths:
            try:
                # Parse filename to get age and gender
                filename_stem = image_path.stem
                parts = filename_stem.split('_')

                if len(parts) < 2:  # Need at least age and gender
                    if self.dataset_dir.joinpath(image_path).is_file():
                        logging.debug(
                            f"Skipping {image_path}: Filename format incorrect (expected age_gender_race_date.jpg)."
                        )
                    continue

                # Parse age
                age_str = parts[0]
                if not age_str.isdigit():
                    if self.dataset_dir.joinpath(image_path).is_file():
                        logging.debug(f"Skipping {image_path}: Age part is not a digit ('{age_str}').")
                    continue

                age = self.validate_age(age_str)
                if age == -1:
                    if self.dataset_dir.joinpath(image_path).is_file():
                        logging.debug(f"Skipping {image_path}: Invalid age value after validation ('{age_str}').")
                    continue

                # Parse gender
                gender_str = parts[1]
                if not gender_str.isdigit() or gender_str not in ['0', '1']:
                    if self.dataset_dir.joinpath(image_path).is_file():
                        logging.debug(f"Skipping {image_path}: Gender part is not 0 or 1 ('{gender_str}').")
                    continue

                gender = int(gender_str)

                # Validate image
                try:
                    img = cv2.imread(str(image_path))
                    if img is None:
                        if self.dataset_dir.joinpath(image_path).is_file():
                            logging.debug(f"Skipping {image_path}: Could not read image file with OpenCV.")
                        continue
                    
                    # For UTK Face, we use the full image as it's already cropped
                    img_h, img_w = img.shape[:2]
                    
                except Exception:
                    continue

                try:
                    # Preserve the full directory structure in relative path
                    relative_path = image_path.relative_to(self.dataset_dir)
                except ValueError:
                    relative_path = Path(image_path.name)

                # For UTK Face, we don't need bbox as images are already cropped
                samples.append(self.create_sample(
                    image_path=image_path,
                    age=age,
                    gender=gender,
                    bbox=None,  # No bbox needed as images are pre-cropped
                    relative_path=relative_path
                ))

            except Exception as e:
                logging.error(f"Error processing {image_path}: {e}")
                continue

        return samples

    def collect_samples_utk_face(self, max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect samples from UTK Face dataset using batch processing.
        Note: UTK Face images are already face-cropped, so no face detection is needed."""
        samples: List[Dict[str, Any]] = []
        logging.info(f"Collecting samples from UTK Face in {self.dataset_dir}")

        # List all image files recursively from the dataset directory
        image_files = []
        for ext in ["*.jpg", "*.png", "*.jpeg"]:
            image_files.extend(list(self.dataset_dir.rglob(ext)))
        
        logging.info(f"Found {len(image_files)} image files (showing first 5):")
        for img in image_files[:5]:
            logging.info(f"  {img.relative_to(self.dataset_dir)}")

        if max_samples is not None and max_samples > 0:
            image_files = image_files[:max_samples]
            logging.info(f"Limited to first {max_samples} images")

        # Split files into batches using the configured batch size
        batches = [image_files[i:i + self.batch_size] for i in range(0, len(image_files), self.batch_size)]
        logging.info(f"Split {len(image_files)} images into {len(batches)} batches of size {self.batch_size}")

        # Process batches in parallel
        with mp.Pool(processes=self.num_workers) as pool:
            results = list(tqdm(
                pool.imap(self.process_utk_face_batch, batches),
                total=len(batches),
                desc="Processing image batches"
            ))

        # Combine results from all batches
        for batch_samples in results:
            samples.extend(batch_samples)

        logging.info(f"Found {len(samples)} valid samples from UTK Face.")
        return samples

    def collect_samples(self, dataset_type: str, max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """Collect samples from the specified dataset type."""
        if dataset_type not in self.dataset_configs:
            logging.error(f"Unknown dataset type: {dataset_type}")
            return []

        if dataset_type == "imdb_clean":
            return self.collect_samples_imdb_clean(max_samples)
        elif dataset_type == "lagenda":
            return self.collect_samples_lagenda(max_samples)
        elif dataset_type == "knn_age":
            return self.collect_samples_knn_age(max_samples)
        elif dataset_type == "hugging_face_age":
            return self.collect_samples_hugging_face_age(max_samples)
        elif dataset_type == "utk_face":
            return self.collect_samples_utk_face(max_samples)
        else:
            logging.error(f"Dataset type {dataset_type} not implemented")
            return []
