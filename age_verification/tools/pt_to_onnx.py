import os
import sys

import torch
import torch.nn as nn
import torch.onnx
import numpy as np
import logging
from typing import Tuple

current_file_path = os.path.abspath(__file__)
tools_dir = os.path.dirname(current_file_path)
project_root = os.path.dirname(tools_dir)

if project_root not in sys.path:
    sys.path.insert(0, project_root)
    print(f"Added to sys path: {tools_dir}")

from mivolo.model.mi_volo import MiVOLO

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
_logger = logging.getLogger(__name__)


def export_to_onnx(
    pytorch_checkpoint_path: str,
    onnx_model_path: str,
    input_size: Tuple[int, int] = (224, 224),  # H, W of thr pre-cropped face input
    num_classes_head: int = 4,  # Num of output logits from model
    device_str: str = "cpu",
    opset_version: int = 12
):
    """
    Loads a trained pytorch model and exports it to onnx format.
    """
    _logger.info(f"Starting ONNX export for checkpoint: {pytorch_checkpoint_path}")
    _logger.info(f"Output onnx path: {onnx_model_path}")
    _logger.info(f"Input size: {input_size}, Num head classes: {num_classes_head}, Opset: {opset_version}")

    try:
        mivolo_wrapper = MiVOLO(
            ckpt_path=pytorch_checkpoint_path,
            device=device_str,
            half=False,
            disable_faces=False,
            use_persons=False,
            verbose=False,
        )
        # get the nn.Module instance
        model_to_export = mivolo_wrapper.model
        _logger.info(f"MiVOLO structure created with in_chans: {mivolo_wrapper.meta.in_chans}"
                     f"and {num_classes_head} head outputs.")
        if mivolo_wrapper.meta.in_chans != 3:
            _logger.warning(f"WARNING: Model structure configured with {mivolo_wrapper.meta.in_chans}"
                            f"input channels. Expected 3 for face only.")
    except Exception as e:
        _logger.error(f"Error instantiating MiVOLO model structure: {e}", exc_info=True)
        return
    
    # Now load the state_dict from actual trained checkpoint
    if not os.path.exists(pytorch_checkpoint_path):
        _logger.error(f"pytorch trained checkpoint not found at: {pytorch_checkpoint_path}")
        return
    
    _logger.info(f"Loading trained state_dict from: {pytorch_checkpoint_path}")
    try:
        checkpoint = torch.load(pytorch_checkpoint_path, map_location=device_str, weights_only=False)

        # The state dict in saved checkpoint may contain prefix model.
        # we need to load checkpoint[model_state_dict] into model_to_export the nn.Module

        if 'model_state_dict' not in checkpoint:
            _logger.error("model_state_dict not found in provided torch checkpoint.")
            return
        
        saved_state_dict = checkpoint['model_state_dict']

        # strip model. prefix from keys if present
        new_state_dict = {}
        keys_had_prefix = any(k.startswith("model.") for k in saved_state_dict.keys())

        if keys_had_prefix:
            _logger.info("Checkpoint keys have model. prefix. Stripping prefix...")
            for k, v in saved_state_dict.items():
                if k.startswith("model."):
                    name = k[len("model."):]
                    new_state_dict[name] = v
                else:
                    new_state_dict[k] = v
            final_state_dict_to_load = new_state_dict
        else:
            final_state_dict_to_load = saved_state_dict
            _logger.info("No model. prefix found in checkpoint keys. Loading as is.")

        model_to_export.load_state_dict(final_state_dict_to_load, strict=True)
        _logger.info("Successfully loaded trained weights into model structure.")
    except Exception as e:
        _logger.error(f"Error loading state_dict from {pytorch_checkpoint_path}: {e}", exc_info=True)
        return
    
    # set model to eval model
    # important for layers like Dropout, BatchNorm
    model_to_export.eval()
    model_to_export.to(torch.device(device_str))

    # Prepare a Dummy Input tensor
    # The shape and type must match what model's forward() method expects
    # For pre cropped faces: (batch_size, 3_channels, height, width)
    # We use batch_size = 1 for export, but will define dynamic batch axis.
    dummy_input = torch.randn(1, 3, input_size[0], input_size[1], device=device_str)
    _logger.info(f"Dummy input tensor created with shape: {dummy_input.shape}")

    # Define input and Output names
    input_names = ["input_image"]  # name for input tensor in ONNX graph
    output_names = ["output_logits"]  # name for output tensor 4 logits

    # Define dynamic axes Important for Flexible batch Size
    # allows onnx model to accept inputs with varying batch sizes
    dynamic_axes = {
        input_names[0]: {0: 'batch_size'},  # First dimension (index 0) of input image is dynamic
        output_names[0]: {0: 'batch_size'}  # First dimension of output logits is dynamic
    }
    _logger.info(f"Dynamic axes configured: {dynamic_axes}")

    # Export the model
    try:
        _logger.info(f"Starting export to ONNX with opset_version={opset_version}...")
        # try:
        #     _logger.info("Tracing model for JIT graph inspection..")
        #     traced_script_module = torch.jit.trace(model_to_export, dummy_input)
        #     _logger.info(traced_script_module.graph)
        # except Exception as e_trace:
        #     _logger.warning(f"error during JIT tracing: {e_trace}")

        torch.onnx.export(
            model_to_export,
            dummy_input,
            onnx_model_path,
            input_names=input_names,
            output_names=output_names,
            dynamic_axes=dynamic_axes,
            opset_version=opset_version,
            export_params=True,  # store the trained parameters weights inside model file
            do_constant_folding=False,  # Execute constant folding for optimization
        )
        _logger.info(f"Model successfully exported to {onnx_model_path}")

        # verify the onnx model
        _logger.info("Attempting to verify onnx model with onnxruntime...")
        verify_onnx_model(pytorch_model=model_to_export,
                          onnx_model_path=onnx_model_path,
                          dummy_input_pytorch=dummy_input)
    except Exception as e:
        _logger.error(f"Error during onnx export or verification: {e}", exc_info=True)


def verify_onnx_model(pytorch_model: nn.Module, onnx_model_path: str, dummy_input_pytorch: torch.Tensor):
    """
    Loads the ONNX model and compares its output with the pytorch model for a dummy input.
    """
    try:
        import onnxruntime

        # pytorch model output
        pytorch_model.eval()
        with torch.no_grad():
            pytorch_outputs = pytorch_model(dummy_input_pytorch.to(next(pytorch_model.parameters()).device))

            if isinstance(pytorch_outputs, tuple):  # If model returns multiple outputs as tuple
                pytorch_outputs_np = pytorch_outputs.cpu().detach().numpy()
        
        # ONNX Runtime session and output
        ort_session = onnxruntime.InferenceSession(onnx_model_path, providers=['CPUExecutionProvider'])

        ort_inputs = {ort_session.get_inputs()[0].name: dummy_input_pytorch.cpu().numpy()}
        ort_outputs = ort_session.run(None, ort_inputs)
        onnx_outputs_np = ort_outputs[0]  # Assuming single output

        # Compare outputs
        if np.allclose(pytorch_outputs_np, onnx_outputs_np, rtol=1e-03, atol=1e-05):
            _logger.info("ONNX model verificaation successfull: Outputs match torch model outputs!")
        else:
            _logger.warning("ONNX model verification FAILED: outputs do not match torch model outputs.")
            _logger.warning(f"Torch output sample: {pytorch_outputs_np.flatten()[:5]}")
            diff = np.abs(pytorch_outputs_np - onnx_outputs_np)
            _logger.warning(f"Max abs diff: {np.max(diff)}")
            _logger.warning(f"Mean abs diff: {np.mean(diff)}")

    except ImportError:
        _logger.warning("Onnxruntime not installed. Skipping onnx model verification")
    except Exception as e:
        _logger.error(f"Error during onnx model verification: {e}", exc_info=True)
    

if __name__ == "__main__":
    TRAINED_PYTORCH_CKPT_PATH = "/home/<USER>/Documents/Anas/jmsc_age_detection/MiVolo_Anas/age_verification/finetuned-MiVOLO/best_model.pth"

    ONNX_OUTPUT_PATH = "/home/<USER>/Documents/Anas/jmsc_age_detection/MiVolo_Anas/age_verification/models/age_verification.onnx"

    CROP_INPUT_SIZE = (224, 224)
    NUM_HEAD_CLASSES = 4
    DEVICE = "cpu"
    ONNX_OPSET = 18

    if not os.path.exists(TRAINED_PYTORCH_CKPT_PATH):
        _logger.error(f"Trained torch checkpoint: {TRAINED_PYTORCH_CKPT_PATH} not found")
    else:
        export_to_onnx(
            pytorch_checkpoint_path=TRAINED_PYTORCH_CKPT_PATH,
            onnx_model_path=ONNX_OUTPUT_PATH,
            input_size=CROP_INPUT_SIZE,
            num_classes_head=NUM_HEAD_CLASSES,
            device_str=DEVICE,
            opset_version=ONNX_OPSET
        )