import argparse
import logging
import cv2
import pandas as pd
import json
from pathlib import Path
from tqdm import tqdm
import numpy as np
from typing import Optional, List, Tuple, Dict, Union


def draw_bbox(image: np.ndarray, bbox: List[int], color: Tuple[int, int, int] = (0, 255, 0), thickness: int = 2) -> np.ndarray:
    """Draw bounding box on image."""
    x0, y0, x1, y1 = bbox
    cv2.rectangle(image, (x0, y0), (x1, y1), color, thickness)
    return image


def find_image_path(image_dir: Path, img_name: str) -> Optional[Path]:
    """Find image path in IMDB dataset structure using absolute paths."""
    try:
        # Convert image_dir to absolute path
        image_dir = image_dir.resolve()
        logging.debug(f"Looking for image {img_name} in directory: {image_dir}")
        
        if '/' in img_name:
            subdir, filename = img_name.split('/', 1)
            # Convert subdir to two-digit format if it's a number
            if subdir.isdigit():
                subdir = f"{int(subdir):02d}"
            
            # Try different possible paths
            possible_paths = [
                image_dir / subdir / filename,  # Direct subdirectory
                image_dir / filename,  # Just the filename
            ]
            
            # Log the paths we're trying
            for path in possible_paths:
                logging.debug(f"Trying path: {path}")
                if path.exists():
                    logging.debug(f"Found image at: {path}")
                    return path
            
            # Try with different extensions
            base_name = filename.rsplit('.', 1)[0]
            for ext in ['.jpg', '.jpeg', '.png']:
                for path in possible_paths:
                    new_path = path.parent / f"{base_name}{ext}"
                    logging.debug(f"Trying path with extension: {new_path}")
                    if new_path.exists():
                        logging.debug(f"Found image with extension at: {new_path}")
                        return new_path
        else:
            # Try direct path if no subdirectory
            direct_path = image_dir / img_name
            logging.debug(f"Trying direct path: {direct_path}")
            if direct_path.exists():
                logging.debug(f"Found image at direct path: {direct_path}")
                return direct_path
                
    except Exception as e:
        logging.error(f"Error finding image path for {img_name}: {e}")
    
    return None


def get_column_mapping(df: pd.DataFrame) -> Dict[str, str]:
    """Get column mapping based on available columns in the CSV."""
    # Define possible column name mappings
    column_mappings = {
        'img_name': ['img_name', 'filename'],
        'face_x0': ['face_x0', 'x_min'],
        'face_y0': ['face_y0', 'y_min'],
        'face_x1': ['face_x1', 'x_max'],
        'face_y1': ['face_y1', 'y_max'],
        'age': ['age'],
        'gender': ['gender']
    }
    
    # Find which columns are present in the DataFrame
    mapping = {}
    for standard_name, possible_names in column_mappings.items():
        for col_name in possible_names:
            if col_name in df.columns:
                mapping[standard_name] = col_name
                break
    
    return mapping


def process_annotations(
    csv_path: Path,
    image_dir: Path,
    output_dir: Path,
    max_images: Optional[int] = None,
    max_age: Optional[int] = None
) -> None:
    """
    Process annotations CSV, find images, draw bounding boxes and save them.
    
    Args:
        csv_path: Path to annotations CSV file
        image_dir: Directory containing images
        output_dir: Directory to save processed images
        max_images: Maximum number of images to process (optional)
        max_age: Maximum age to process (optional, will only process images with age <= max_age)
    """
    # Convert paths to absolute
    csv_path = csv_path.resolve()
    image_dir = image_dir.resolve()
    output_dir = output_dir.resolve()
    
    logging.info(f"CSV path: {csv_path}")
    logging.info(f"Image directory: {image_dir}")
    logging.info(f"Output directory: {output_dir}")
    
    if not csv_path.exists():
        logging.error(f"CSV file not found: {csv_path}")
        return

    if not image_dir.exists():
        logging.error(f"Image directory not found: {image_dir}")
        return

    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)

    try:
        # Read CSV file
        df = pd.read_csv(csv_path)
        
        # Get column mapping
        column_mapping = get_column_mapping(df)
        logging.info(f"Using column mapping: {column_mapping}")

        # Verify required columns are present
        required_columns = ['img_name', 'face_x0', 'face_y0', 'face_x1', 'face_y1']
        missing_columns = [col for col in required_columns if col not in column_mapping]
        if missing_columns:
            logging.error(f"Missing required columns in CSV. Need {missing_columns}")
            return

        # Filter by age if max_age is specified
        if max_age is not None and 'age' in column_mapping:
            df = df[df[column_mapping['age']] <= max_age]
            logging.info(f"Filtered to {len(df)} images with age <= {max_age}")
            if len(df) == 0:
                logging.error(f"No images found with age <= {max_age}")
                return

        # Apply max_images limit after age filtering
        if max_images is not None:
            df = df.head(max_images)
            logging.info(f"Processing first {max_images} images")

        # Log first few image names from CSV
        logging.info("First few image names from CSV:")
        for img_name in df[column_mapping['img_name']].head(5):
            logging.info(f"  - {img_name}")

        processed_count = 0
        not_found_count = 0
        
        # Process each row
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing images"):
            try:
                img_name = str(row[column_mapping['img_name']])
                
                # Find image path
                image_path = find_image_path(image_dir, img_name)
                if image_path is None:
                    not_found_count += 1
                    if not_found_count <= 5:  # Log only first 5 not found images
                        logging.warning(f"Image not found: {img_name}")
                    continue

                # Read image
                image = cv2.imread(str(image_path))
                if image is None:
                    logging.warning(f"Could not read image: {image_path}")
                    continue

                # Get bbox coordinates
                bbox = [
                    int(row[column_mapping['face_x0']]),
                    int(row[column_mapping['face_y0']]),
                    int(row[column_mapping['face_x1']]),
                    int(row[column_mapping['face_y1']])
                ]

                # Draw bbox
                image_with_bbox = draw_bbox(image.copy(), bbox)

                # Add age and gender if available
                if 'age' in column_mapping:
                    age = row[column_mapping['age']]
                    cv2.putText(
                        image_with_bbox,
                        f"Age: {age}",
                        (bbox[0], bbox[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.5,
                        (0, 255, 0),
                        2
                    )

                if 'gender' in column_mapping:
                    gender_val = row[column_mapping['gender']]
                    # Handle both numeric and string gender values
                    if isinstance(gender_val, (int, float)):
                        gender = "M" if gender_val == 1 else "F" if gender_val == 0 else "Unknown"
                    else:
                        gender = str(gender_val).upper()
                    cv2.putText(
                        image_with_bbox,
                        f"Gender: {gender}",
                        (bbox[0], bbox[1] - 30),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.5,
                        (0, 255, 0),
                        2
                    )

                # Save image with absolute path
                output_path = output_dir / f"vis_{image_path.name}"
                cv2.imwrite(str(output_path), image_with_bbox)

                processed_count += 1
                if max_images is not None and processed_count >= max_images:
                    break

            except Exception as e:
                logging.error(f"Error processing image {img_name}: {e}")

        logging.info(f"Successfully processed {processed_count} images")
        if not_found_count > 0:
            logging.info(f"Could not find {not_found_count} images")

    except Exception as e:
        logging.error(f"Error reading CSV file: {e}")


def process_json_annotations(
    json_path: Path,
    output_dir: Path,
    max_images: Optional[int] = None,
    max_age: Optional[int] = None
) -> None:
    """
    Process annotations JSON file, draw bounding boxes and save them.
    
    Args:
        json_path: Path to annotations JSON file
        output_dir: Directory to save processed images
        max_images: Maximum number of images to process (optional)
        max_age: Maximum age to process (optional, will only process images with age <= max_age)
    """
    # Convert paths to absolute
    json_path = json_path.resolve()
    output_dir = output_dir.resolve()
    
    logging.info(f"JSON path: {json_path}")
    logging.info(f"Output directory: {output_dir}")
    
    if not json_path.exists():
        logging.error(f"JSON file not found: {json_path}")
        return

    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)

    try:
        # Read JSON file
        with open(json_path, 'r') as f:
            annotations = json.load(f)
        
        # Convert to DataFrame for easier processing
        df = pd.DataFrame(annotations)
        
        # Filter by age if max_age is specified
        if max_age is not None and 'age' in df.columns:
            df = df[df['age'] <= max_age]
            logging.info(f"Filtered to {len(df)} images with age <= {max_age}")
            if len(df) == 0:
                logging.error(f"No images found with age <= {max_age}")
                return

        # Apply max_images limit after age filtering
        if max_images is not None:
            df = df.head(max_images)
            logging.info(f"Processing first {max_images} images")

        processed_count = 0
        not_found_count = 0
        
        # Process each row
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing images"):
            try:
                image_path = Path(row['image_path'])
                
                if not image_path.exists():
                    not_found_count += 1
                    if not_found_count <= 5:  # Log only first 5 not found images
                        logging.warning(f"Image not found: {image_path}")
                    continue

                # Read image
                image = cv2.imread(str(image_path))
                if image is None:
                    logging.warning(f"Could not read image: {image_path}")
                    continue

                # Get bbox coordinates
                bbox = [
                    int(row['face_x0']),
                    int(row['face_y0']),
                    int(row['face_x1']),
                    int(row['face_y1'])
                ]

                # Draw bbox
                image_with_bbox = draw_bbox(image.copy(), bbox)

                # Add age and gender if available
                if 'age' in row:
                    age = row['age']
                    cv2.putText(
                        image_with_bbox,
                        f"Age: {age}",
                        (bbox[0], bbox[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.5,
                        (0, 255, 0),
                        2
                    )

                if 'gender' in row:
                    gender_val = row['gender']
                    # Handle both numeric and string gender values
                    if isinstance(gender_val, (int, float)):
                        gender = "M" if gender_val == 1 else "F" if gender_val == 0 else "Unknown"
                    else:
                        gender = str(gender_val).upper()
                    cv2.putText(
                        image_with_bbox,
                        f"Gender: {gender}",
                        (bbox[0], bbox[1] - 30),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.5,
                        (0, 255, 0),
                        2
                    )

                # Save image with absolute path
                output_path = output_dir / f"vis_{image_path.name}"
                cv2.imwrite(str(output_path), image_with_bbox)

                processed_count += 1
                if max_images is not None and processed_count >= max_images:
                    break

            except Exception as e:
                logging.error(f"Error processing image {image_path}: {e}")

        logging.info(f"Successfully processed {processed_count} images")
        if not_found_count > 0:
            logging.info(f"Could not find {not_found_count} images")

    except Exception as e:
        logging.error(f"Error reading JSON file: {e}")


def main():
    parser = argparse.ArgumentParser(description="Process images with bounding boxes from annotations")
    parser.add_argument("--input", type=str, required=True, help="Path to annotations file (CSV or JSON)")
    parser.add_argument("--image-dir", type=str, help="Directory containing images (required for CSV input)")
    parser.add_argument("--output-dir", type=str, required=True, help="Directory to save processed images")
    parser.add_argument("--max-images", type=int, help="Maximum number of images to process")
    parser.add_argument("--max-age", type=int, help="Maximum age to process (will only process images with age <= max_age)")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    input_path = Path(args.input)
    
    if input_path.suffix.lower() == '.json':
        if args.image_dir:
            logging.warning("--image-dir is not needed for JSON input as it contains absolute paths")
        process_json_annotations(
            json_path=input_path,
            output_dir=Path(args.output_dir),
            max_images=args.max_images,
            max_age=args.max_age
        )
    elif input_path.suffix.lower() == '.csv':
        if not args.image_dir:
            logging.error("--image-dir is required for CSV input")
            return
        process_annotations(
            csv_path=input_path,
            image_dir=Path(args.image_dir),
            output_dir=Path(args.output_dir),
            max_images=args.max_images,
            max_age=args.max_age
        )
    else:
        logging.error("Input file must be either CSV or JSON")


if __name__ == "__main__":
    main() 