import argparse
import logging
import os
import random
from typing import List, Optional, Tuple
import numpy as np

from PIL import Image

import torch
import torchvision
# torch backend settings
import torch.backends
# setting for cuda backend optimization
import torch.backends.cudnn
import torch.nn as nn  # Neural network modules like layers, loss func
# functions like activation, pooling, and loss calculations
import torch.nn.functional as F
import torch.optim as optim  # Optimization algorithms and learning rate schedulers
from torch.utils.data import DataLoader  # Utility for batching and loading data from datasets

from tqdm import tqdm
import wandb
import wandb.util

from sklearn.metrics import precision_recall_fscore_support, accuracy_score, confusion_matrix as sk_confusion_matrix

from mivolo.model.mi_volo import MiVOLO  # handles detection and cropping
from mivolo.model.yolo_detector import Detector  # yolo face/person detector
from data.dataset import PreCroppedFaceDataset
from mivolo.structures import PersonAndFaceResult


# Logging configurations
# setting yolov8's logs to warnings to avoid spam from the detector during training
# mivolo to info level for important updates
logging.getLogger("yolov8").setLevel(logging.WARNING)
logging.getLogger("mivolo").setLevel(logging.INFO)
# root logger will be set later base on the debug flag


def get_parser():
    parser = argparse.ArgumentParser(description="Mivolo For age classification")

    # Model and checkpoint
    parser.add_argument("--precrop_dataset", action="store_true", default=True)
    parser.add_argument("--data_path",
                        default="/home/<USER>/Documents/Anas/jmsc_age_detection/dataset")
    parser.add_argument("--manifest_file", type=str, default="annotations.json")
    parser.add_argument("--checkpoint", default="models/volo_d1.tar")

    parser.add_argument("--detector-weights", default="models/yolov8x_person_face.pt")
    parser.add_argument("--output_dir", default="finetuned-MiVOLO")
    # parser.add_argument("--resume", type=str, default="finetuned-MiVOLO/best_model.pth")
    parser.add_argument("--resume", type=str)

    # Model hyperparameters
    parser.add_argument("--finetune-backbone", action="store_true", default=True)
    parser.add_argument("--batch_size", default=128, type=int)
    parser.add_argument("--epochs", default=20, type=int)
    parser.add_argument("--lr", default="1.5e-4", type=float,
                        help="Peak learning rate for optimizer.")
    parser.add_argument("--drop_path_rate", default=0.32, type=float)
    parser.add_argument("--weight-decay", default="0.05", type=float,
                        help="L2 penalty used by the optimizer.")
    parser.add_argument("--warmup_epochs", default=1, type=int)
    parser.add_argument("--label-smoothing", type=float, default=0.1)

    # System and logging
    parser.add_argument("--device", default="cuda")
    parser.add_argument("--num-workers", default=4, type=int, help="Number of workers for loadig data")
    parser.add_argument("--log-interval", default=20, type=int)
    parser.add_argument("--save-interval", default=1, type=int)
    parser.add_argument("--eval_interval", default=1, type=int)
    parser.add_argument("--debug", action="store_true", default=False)
    parser.add_argument("--seed", default=42)

    return parser


class WandbLogger:
    def __init__(self, args):
        self.args = args
        # ensure it's initialized once
        if wandb.run is None:
            run_name = f"MiVOLO-ft-AgeGender-{wandb.util.generate_id()}"  # Generate a unique name
            wandb.init(project="Age Verification", entity="anas-jmscpos-jmsc", name=run_name, config=vars(self.args))
        # Ensure run has a name set
        if wandb.run.name is None:
            wandb.run.name = wandb.run.id
            logging.info(f"Wandb run initialized: {wandb.run.name}")

    def log_metrics(self, metrics, step=None):
        "Log dictionary of metrics to wandb"
        # the step parameter is important for plotting metrics correctly over time/progress
        wandb.log(metrics, step=step)

    def log_image_with_boxes(self, image, boxes, caption="", step=None):
        """
        Logs an image with bounding boxes to wandb

        Args:
            image np array of shape H, W, C
            boxes: list of x1, y1, x2, y2 coordinates
            caption: optional caption for the image
            step: optional step for the image
        """
        # convert image to wandb image
        boxes_data = []
        # assuming class id 0 is always face
        class_id_map = {0: "Face"}
        for box in boxes:
            x1, y1, x2, y2 = box
            boxes_data.append({
                "position": {
                    "minX": x1,
                    "maxX": x2,
                    "minY": y1,
                    "maxY": y2
                },
                "class_id": 0,
                "box_caption": "Face"
            })
        if image.dtype != np.uint8:
            if image.dtype != np.uint8:
                if image.max() <= 1.0:
                    # Image is in [0, 1] range
                    image = (image * 255).astype(np.uint8)
                else:
                    image = image.astype(np.uint8)
        image = Image.fromarray(image)  # convert numpy array to PIL image
        image = torchvision.transforms.Resize((224, 224))(image)
        wandb_image = wandb.Image(
            image,
            boxes={
                "predictions": {
                    "box_data": boxes_data,
                    "class_labels": class_id_map
                }
            },
            caption=caption
        )
        wandb.log({"image_with_boxes": wandb_image}, step=step)

    def log_crops(self,
                  crops: List[Optional[np.ndarray]],  # List of uint8 HWC numpy crop images
                  model_outputs: Optional[torch.Tensor],  # Model output logits (B, 4) or (num_crops, 4)
                  caption_prefix: str = "",
                  step: Optional[int] = None,
                  max_crops_to_log: int = 5):
        """
        Logs image crops to wandb, with age/gender predictions overlaid.

        Args:
            crops: list of np arrays representing image crops
            model_outputs: Tensor of model output logits corresponding to the crops.
                           Expected shape (num_crops, 4) where 4 = [g0, g1, a0, a1]
            caption: optional caption for the image
            step: optional step for the image
        """
        if not crops:
            logging.debug("No crops to log. Skipping.")
            return

        logged_count = 0
        num_available_crops = len(crops)
        num_available_logits = model_outputs.shape[0] if model_outputs is not None else 0

        # Ensure we don't try to access logits out of bounds if counts mismatch
        num_iterations = min(num_available_crops,
                             num_available_logits if model_outputs is not None else num_available_crops)

        # wandb_images = []
        for i in range(num_iterations):
            # try:
            # stop if we have reached maximum number to log
            if logged_count >= max_crops_to_log:
                logging.debug(f"Reached maximum number of crops to log ({max_crops_to_log}). Skipping rest.")
                break

            crop = crops[i]

            if crop is None:
                logging.debug(f"Skipping None crop at index {i}")
                continue
            # ensure crop is a valid image
            if not isinstance(crop, np.ndarray) or crop.ndim != 3 or crop.shape[2] != 3 or crop.dtype != np.uint8:
                logging.warning(f"Crop {i} is not a numpy array, type: {type(crop)}")
                continue

            # get predictions from logits if available
            age_label_text = "Age: N/A"
            gender_label_text = "Gender: N/A"
            pred_caption = ""

            if model_outputs is not None and i < num_available_logits:
                # get the logits for this crop
                logits = model_outputs[i].detach().cpu()

                # Age prediction binary classification 0: child, 1:adult
                age_cls_logits = logits[2:4]
                age_pred_class = torch.argmax(age_cls_logits).item()
                age_label_text = "Adult" if age_pred_class == 1 else "Child"

                # Gender prediction 0: male, 1: female, assumption*
                gender_cls_logits = logits[0:2]
                gender_pred_class = torch.argmax(gender_cls_logits).item()
                gender_label_text = "Female" if gender_pred_class == 1 else "Male"

                pred_caption = f"\nPred: {age_label_text}, {gender_label_text}"

            caption = f"{caption_prefix} Crop {i}{pred_caption}"
            # create wandb.Image for single crop
            wandb_image = wandb.Image(crop, caption=caption)

            # log ind with unique key
            log_key = f"{caption_prefix}_crop_{i}_pred"
            wandb.log({log_key: wandb_image}, step=step)
            logged_count += 1

            # except Exception as e:
            #     logging.error(f"Error processing crop {i}: {e}")

    def log_hyperparameters(self, args):
        """Logs the command line arguments (hyperparamters) to wandb config"""
        wandb.config.update(vars(args))

    def watch_model(self, model, log="gradients", log_freq=1000):
        """Configures wandb to watch the mdoel, logging parameters and gradients"""
        # useful for training issues like vanishing/exploding gradients
        wandb.watch(model, log=log, log_freq=log_freq)


# creating model, detecotr , modifying the model
# setting up the optimizer and scheduler and processing data samples through the model
class ModelManager:
    def __init__(self, args):
        self.args = args
        self.device = args.device
        self.detector = None

        # Instantiate the main MiVOLO model wrapper
        # This wrapper internally creates the MiVOLO model
        # we pass the num_class=4 here so the internal mivolo model's final head layer is created with 4 outputs
        # 2 for binary gender, 2 for ages (classification)
        # logging.info(f"Instantiating MiVOLO model: {args.model}")
        try:
            self.model = MiVOLO(
                # model_name=args.model,  # Specifies the core architecute (e.g. mivolo_d1_224)
                ckpt_path=self.args.checkpoint,  # we will load it manually later with strict=False to ignore the head
                device=str(self.device),
                half=False,  # use full precision (float32) for training
                disable_faces=False,
                use_persons=False,
                verbose=True,
                drop_path_rate=self.args.drop_path_rate  # Explicitly set drop_path_rate to a float value
            )
            logging.info("MiVOLO model instantiated successfully")
        except Exception as e:
            logging.error(f"Error instantiating MiVOLO model: {e}")
            raise

        # Now modify the internal neural network instance (MiVOLO)
        # contained within the MiVOLO wrapper (self.model.model)
        self._modify_model(self.model.model)

        # Optimizer and scheduler are initialized as None for later
        # once the dataloaders are ready to determine the number of training steps
        self.optimizer = None
        self.scheduler = None

        # Move the entire mivolo model to specified device
        self.model = self.model.to(self.device)

    def _modify_model(self, mivolo_model: nn.Module):
        """
        Modifies the core mivolo model instance
        1. Loads the initial pre-trained weights into the backbone, skipping the original head.
        2. Randomly initializes the *new head
        (which already has 4 outputs because we specified num_classes=4 in mivolo init)
        3. Freeezes all parameters except those in the new head
        """
        logging.info("Modifying internal MiVOLO head for 4-way classification and freezing backbone")

        assert hasattr(mivolo_model, 'head'), "Mivolo model instance does not have a head attribute."
        # The new head mivolo_model.head was already created with 4 outputs
        # with num_classes=4. We now explicitly initialize it's weights randomly
        # Important because even with strict=false. if the original head had >= 4 ouputs
        # some initial values might have been loaded. Random initalization ensures it starts fresh for the new task
        logging.info("Randomly initializing the new 4 output head weights (2 for gender, 2 for age classification)")

        # Kaiming Normal initialization is common for layers followed by ReLU
        # which is typical after the linear head before loss
        nn.init.kaiming_normal_(mivolo_model.head.weight, mode="fan_out", nonlinearity="relu")
        # initialize biases to zero
        nn.init.zeros_(mivolo_model.head.bias)
        logging.info("New head weights initialized successfully")

        # Set requires_grad based on Finetuning modde
        if not self.args.finetune_backbone:
            # Head only training (Default)
            # Freeze all parameters in the core MiVOLO model backbone, neck, etc
            # this prevents gradients from updating them during training
            logging.info("Freezing all mivolo model parameters except head...")
            for name, param in mivolo_model.named_parameters():
                param.requires_grad = False  # set required_grad to False to freeze

            # unfreeze only the parameters of the new head
            # these are the only parameters that the optimizer will update
            # logging.info("unfreezing only the new head parameters for training..")
            try:
                for name, param in mivolo_model.head.named_parameters():
                    param.requires_grad = True  # True to unfreeze
                    if self.args.debug:
                        # Log trainable parameters if debug is on
                        logging.debug(f"Parameter '{name}' in head is now trainable")
                        # logging.debug(f"Parameter '{name}' in is now trainable")
            except AttributeError:
                logging.error("MiVOLO model instance does not have a head attribute. Cannot unfreeze head")
                raise  # Critical error
        else:
            # Full finetuning
            logging.info("Full Finetuning is enabled.")
            # unfreeze all parameters in MiVOLO Model
            for name, param in mivolo_model.named_parameters():
                param.requires_grad = True

        # verify how many parameters are trainable after freezing/unfreezing
        total_params = sum(p.numel() for p in mivolo_model.parameters())  # Total parameters
        # parameters with requires_grad=True
        trainable_params = sum(p.numel() for p in mivolo_model.parameters() if p.requires_grad)
        logging.info(f"MiVOLO model parameters - Total: {total_params:,}, Trainable: {trainable_params:,}")
        if trainable_params == 0:
            logging.error("No trainable parameters found. Check model modification and freezing logic")
        # Optional: Log names and shape of trainable parameters in debug mode
        # if self.args.debug:
        #     logging.debug("Trainable parameters list:")
        #     for name, param in mivolo_model.named_parameters():
        #         if param.requires_grad:
        #             logging.debug(f"- {name}, shape: {param.shape}")

    def _create_detector(self):
        """Creates and loads the object detector model
        The detector finds faces and persons in the input images.
        Its output {bounding boxes} is then used by MiVOLO wrapper's prepare_crops method
        """
        logging.info("Creating detector...")
        try:
            detector = Detector(
                self.args.detector_weights,
                device=self.args.device,
                half=False,
                verbose=False
            )
            # detector.eval()
            logging.info(f"Detector loaded from {self.args.detector_weights} and set to eval mode")
            # ensure detector parameters are frozen they should be default in Detector class used this way
            # for param in detector.parameters():
            #     param.requires_grad = False
            return detector
        except Exception as e:
            logging.error(f"Error laoding detector: {e}")
            raise

    def create_optimizer(self):
        """
        Creates the optimizer.
        The optimzer is responsible for updating the model's parameters based on the gradients
        calculated during backward pass.
        Crucially it is initialized ONLY with the parameters that requries gradients i.e. the unfrozen head
        """
        # collect all parameters from the entire mivolo wrapper model self.model
        # tha have requires_grad=true. Because we froze the backbone and unfroze the head
        # this list should contain only the head parameters
        params_to_update = [p for p in self.model.model.parameters() if p.requires_grad]

        if not params_to_update:
            logging.error("Optimizer creation failed: No trainable parameters found!")
            raise RuntimeError("No parameters found. Check the model modification and freezing logic")

        logging.info(f"Optimizer created for {len(params_to_update)} trainable parameter tensors")
        # use AdamW optimizer, which is common and effective choice
        self.optimizer = optim.AdamW(
            params_to_update,  # provide only the parameters to update
            lr=self.args.lr,  # set peak learning rate
            weight_decay=self.args.weight_decay,  # set L2 regularization
            betas=(0.9, 0.999),  # AdamW specific parameters
            eps=1e-8  # specific to Adamw
        )
        # The scheduler for learnign rate adjustment will be created after we know total number of training steps.

    def create_scheduler(self, train_loader_len):
        """
        Creates the learning rate scheduler.
        Initializes it with different learning rates for the head and backbone
        if full finetuning is enabled.
        We use OneCycleLR which cycles the learning rate up and down over the training duration.
        This often helps with the training stability and performance.
        """
        # collect all prarameters that have requires_grad=True
        trainable_params = [p for p in self.model.model.parameters() if p.requires_grad]

        if not trainable_params:
            logging.error("Optimizer creation failed. No trainable parameters found!")
            raise RuntimeError("No trainable parameters found. Check model modification and freezing logic")

        # logging.info(f"Optimizer created for {len(trainable_params)} trainable parameter tensors")

        steps_per_epoch = train_loader_len  # number of batches in one epoch
        total_steps = steps_per_epoch * self.args.epochs  # Total training steps

        if total_steps == 0:
            logging.warning("Train loader is empty or epochs are zero. Cannot create scheduler")
            self.scheduler = None
            return
        if self.optimizer is None:
            logging.warning("Optimizer not created. Cannot create scheduler")
            self.scheduler = None
            return

        logging.info(f"""Creating OneCycleLr scheduler for {self.args.epochs} epochs,
                     {steps_per_epoch} steps/epoch, total {total_steps} total steps""")
        self.scheduler = optim.lr_scheduler.OneCycleLR(
            self.optimizer,
            max_lr=self.args.lr,  # The peak learning rate to reaach
            epochs=self.args.epochs,
            steps_per_epoch=steps_per_epoch,  # Number of steps (batches) in each epoch
            pct_start=self.args.warmup_epochs / self.args.epochs,  # Fraction of total steps for warmup
            div_factor=25.0,  # Intial LR will be max_lr / div_factor
            final_div_factor=1e4,  # Final LR will be max_lr / (div_factor * final_div_factor)
            anneal_strategy="cos"  # Use cosine annealing for the decay phase
        )

    def train_mode(self):
        """sets the model and its internal components used for training to train mode"""
        # This enables things like drop out and batch normalization specific to training
        self.model.train()  # sets the wrapper to train mode
        self.model.model.train()  # Explicitly set the core MiVOLO to train mode
        # self.detector.eval()
        logging.debug("Model manager set to train mode")

    def eval_mode(self):
        """Sets the model and its internal componets used for evaluation to eval mode"""
        # This disables dropout and uses population statistics for batch normalization
        # it also disables gradient calculation torch.no_grad will be used
        self.model.eval()
        self.model.model.eval()
        # self.detector.eval()
        logging.debug("Model Manager to eval mode")

    def process_single_sample(self,
                              # This is Normalized: C, H, W, approx [-2.1, 2.6]
                              img_tensor: torch.Tensor,
                              target_dict: dict,
                              sample_idx: int = -1,
                              global_step: int = -1,
                              original_size: Optional[Tuple[int, int]] = None):
        """
        Process a single image and its corresponding ground truth targets
        This involves running the detector, preparing face/crops
        feeding the crops through the finetuned Mivolo backbone and head
        and calculating the loss and accuracy for the detections that have corresponding labels

        Args:
        img_tensor (torch.Tensor): the image tensor (C, H, W, range 0-1)
        target_dict (dict): Dictionary containing ground truth labels and boxes for this image
        sample_idx (int): Index of sample within its batch for debugging logs
        global_step (int): Global step for logging
        original_size: Original Image size (H, W) that the json boxes coordinates cooresponds to.

        Returns:
        tuple: (losss_tensor_for_sample, age_correct_count, gender_correct_count, detections_count)
                loss_tensor_for_sample (tensor): Sum of loss tensors for all valid detections in this samples.
                                                 Needs requires_grad = True if used for training
                age_correct_count (int): Number of age predictions that were correct
                detection_count (int): Number of detections that were successfully processed
                and matched with a ground truth label
        """
        device = self.device
        log_prefix = f"[Sample {sample_idx}]"

        # Input img_tensor is assumed to be CxHxW from DataLoader (collate_fn=list)
        if img_tensor.ndim == 3:  # CHW
            model_input_crops = img_tensor.unsqueeze(0).to(device)  # Make it 1xCxHxW
        elif img_tensor.ndim == 4 and img_tensor.shape[0] == 1:  # Already 1CHW
            model_input_crops = img_tensor.to(device)
        else:
            logging.error(f"{log_prefix} Unexpected input tensor shape: {img_tensor.shape}")
            return torch.tensor(0., device=device, requires_grad=True), 0, 0, 0, [], []

        if self.args.debug:
            logging.debug(f"{log_prefix} model_input_crops shape: {model_input_crops.shape} "
                          f"(min: {model_input_crops.min().item():.4f}, max: {model_input_crops.max().item():.4f})")

        try:
            # nn.Module call for training
            model_output = self.model.model(model_input_crops)
            if self.args.debug:
                logging.debug(f"{log_prefix} Model output shape: {model_output.shape}")
        except Exception as e:
            logging.error(f"{log_prefix} Error during model forward pass: {e}", exc_info=True)
            return torch.tensor(0., device=device, requires_grad=True), 0, 0, 0, [], []

        # the output is raw logits for each detection. Shape (num_processed_detections, 4)
        # calculate metric loss and accuracy based on the model's output logits
        # and ground truth labels from the original target dictionary
        # origianl_indicies_list tells us which ground truth label corresponds to which row in model_outptu
        sample_total_loss_t, sample_age_correct, sample_gender_correct, \
            processed_detections_count, sample_true_age_labels_cm, \
            sample_predicted_age_probs_cm = self._calculate_loss(
                model_output,
                None,  # No detected bbox object
                [0],  # The current crop is index 0 relative to its own labels in target dict
                [0],  # Type is face
                target_dict,  # target_dict["ages"][0], target_dict["genders"][0]
                device,
                log_prefix,
            )

        if self.args.debug:
            # Log the total scalar loss value for the sample
            logging.debug(f"""{log_prefix} Sample metrics - Loss (sum item): {sample_total_loss_t.item():.4f},
                          Age Correct: {sample_age_correct}, Gender Correct: {sample_gender_correct},
                          Valid Detections: {processed_detections_count} """)

        # Return the calculated metrics and loss tensor for this sample
        return (sample_total_loss_t, sample_age_correct, sample_gender_correct,
                processed_detections_count, sample_true_age_labels_cm, sample_predicted_age_probs_cm)

    def _calculate_loss(self,
                        output: torch.Tensor,
                        detected_objects: PersonAndFaceResult,
                        crop_to_detection_indices_list: List[Optional[int]],
                        crop_type_indices: List[int],
                        target: dict,
                        device: torch.device,
                        log_prefix: str):
        """
        Calculate classification loss and correctness for processed crops
        by matching detected face boxes to ground truth face boxes using IoU.
        Only calculates loss for prediction derived from face detections.

        Args:
            output (torch.Tensor): Model output tensor for detectiions from one sample (num_detections, 4).
                                    Shape: (Number of processed detections, 4 logits)
            detected_objects (list): List of indices mapping rows in output back to
                                          indices in the origianl target lists (ages, genders)
            target (dict): The ground truth target dictionary for the current image sample.
            device,
            log_prefix

        Returns:
            tuple: (total_loss_tensor_for_sample, age_correct_count, gender_correct_count, processed_detections_count,
            true_age_labels_for_cm, predicted_age_probs_for_cm)
                    total_loss_tensor_for_sample (tensor): A tensor representing the sum of losses for ALL valid
                                                            detectionss in this single sample. This is needed
                                                            to accumlate gradients correctly across detections.
                    age_correct_count (int): Total count of age predictions correct in this sample.
                    gender_correct_count (int): Total count of gender predictions correct in this sample
                    processed_detections_count (int): Total count of detections successfully processed 
                    and used for metrics/loss
        """
        # -- Validation ---
        num_outputs = output.shape[0] if output is not None else 0
        num_indices = len(crop_to_detection_indices_list) if crop_to_detection_indices_list is not None else 0
        num_types = len(crop_type_indices) if crop_type_indices is not None else 0
        if output is None or num_outputs == 0 or not (num_outputs == num_indices == num_types):
            if self.args.debug:
                logging.debug(f"{log_prefix} Invalid input for loss calculation. Returning zeros.")
            return torch.tensor(0., device=device, requires_grad=True), 0, 0, 0, [], []

        # split logits
        gender_logits = output[:, 0:2]
        age_logits = output[:, 2:4]

        list_of_loss_tensors = []
        age_correct_count = 0
        gender_correct_count = 0
        processed_detections_count = 0
        # For batch level CM logging
        true_age_labels_for_cm = []
        # store probs e.g. softmax ouput
        predicted_age_probs_for_cm = []

        # get Ground Truth Data
        gt_ages_list = target.get("ages", [])
        gt_genders_list = target.get("genders", [])

        # Iterate through each model prediction each crop
        #  i is the index for the current crop's prediction in the output tensor
        for i in range(num_outputs):
            # get the original detection index from which this crop was derived
            original_gt_idx = crop_to_detection_indices_list[i]
            # get the type of original detection 0 face, 1 person
            detection_type = crop_type_indices[i]

            # Skip if not a FACE crop, or if original gr idx is None
            if original_gt_idx is None or detection_type != 0:
                if self.args.debug:
                    logging.debug(f"{log_prefix} Skipping prediction {i}: Invalid original detection index or type")
                elif self.args.debug and original_gt_idx is None:
                    logging.debug(f"{log_prefix} Skipping prediction {i}: Original GT index is None")
                continue

            # verify the original detection index is within the bounds of the GT lists
            if not (0 <= original_gt_idx < len(gt_ages_list)
                    and 0 <= original_gt_idx < len(gt_genders_list)):
                if self.args.debug:
                    logging.debug(f"{log_prefix} Skipping prediction {i}: Original GT index out of bounds")
                    continue

            # Retrieve the GT age and gender labels using the best match gt idx
            try:
                age_value_gt = target["ages"][original_gt_idx]  # Raw age value for the dataset e.g. 25, 10
                AGE_THRESHOLD = 30
                age_lbl = -1
                # convert numerical age into binary
                # 0: child, 1: adult
                try:
                    # attempt to convert to float first;
                    # this handles int, float and numeric strings
                    numeric_age = float(age_value_gt)
                    age_lbl = 1 if numeric_age > AGE_THRESHOLD else 0
                except (ValueError, TypeError):
                    # if direct float conversion fails, it might be an error or already binary
                    # if dataset guarantees numeric ages, this path might indicate bad data.
                    # for robustness, you could check if it's already 0 or 1 as a string/int
                    if str(age_value_gt).strip() == "0" or age_value_gt == 0:
                        age_lbl = 0
                    elif str(age_value_gt).strip() == "1" or age_value_gt == 1:
                        age_lbl = 1
                    else:
                        # if neither convertible to float nor already binary 0/1, it's an issue
                        raise ValueError(
                            f"Cannot interpret GT age value: '{age_value_gt}' (type: {type(age_value_gt)})")

                gender_int_gt = target["genders"][original_gt_idx]
                gender_lbl = -1  # Default for unknown or invalid
                if gender_int_gt == 0:
                    gender_lbl = 0  # male
                elif gender_int_gt == 1:
                    gender_lbl = 1  # female
                if self.args.debug:
                    logging.debug(f"""{log_prefix} GT labels for matched GT index {original_gt_idx}:
                                  Age: {age_value_gt} ({age_lbl})""")

            except Exception as e:
                logging.warning(f"""{log_prefix} Skipping detection {i}: {e}""")
                continue

            # calculate loss for this matched prediction
            try:
                # get the model's output logits for the current crop i
                current_age_logits = age_logits[i].unsqueeze(0)  # Shape[1, 2]
                current_gender_logits = gender_logits[i].unsqueeze(0)

                # create pytorch longTensors for gt labels
                # crosEntropy expects long tensor targets
                age_label_t = torch.tensor([age_lbl], device=device, dtype=torch.long)  # Shape [1]
                # gender_label_t = torch.tensor([gender_lbl], device=device, dtype=torch.long)

                # calculate3 CrossEntropy loss for age and gender separately.
                age_loss_t = F.cross_entropy(
                    current_age_logits,
                    age_label_t,
                    label_smoothing=self.args.label_smoothing
                )
                # Combine losses e.g simple sum or weighted sum
                detection_loss_t = age_loss_t

                # For monitoring, default to 0
                gender_loss_t = torch.tensor(0.0, device=device)

                # Calculate gender loss
                if gender_lbl != -1:  # only if GT gender is known 0 or 1
                    current_gender_logits = gender_logits[i].unsqueeze(0)
                    gender_label_t = torch.tensor([gender_lbl], device=device, dtype=torch.long)
                    try:
                        gender_loss_t = F.cross_entropy(
                            current_gender_logits,
                            gender_label_t,
                            label_smoothing=self.args.label_smoothing
                        )
                        # include in backpropagation
                        detection_loss_t = age_loss_t + gender_loss_t
                    except RuntimeError as e:
                        logging.error(f"""{log_prefix} RuntimeError calculating gender loss for pred
                                      (GT gender_lbl: {gender_lbl}, Logits: {current_gender_logits.tolist()}): {e}""")

                # store the loss tensor for this specific detection
                # Gradients will flow through these when summed and .backward() is called
                list_of_loss_tensors.append(detection_loss_t)
            except Exception as e:
                logging.error(
                    f"{log_prefix} Error calculating loss for prediction {i}: {e}.Skipping this prediction",
                    exc_info=True)
                continue

            # Calculate Accuracy and store data for confusion matrix
            # perform accuracy calculation outside of gradient tracking
            with torch.no_grad():
                # Get the predicted class index by taking argmax of the logits
                age_pred_class = torch.argmax(current_age_logits, dim=1).item()
                gender_pred_class = torch.argmax(current_gender_logits, dim=1).item()

                # Increment correct counts if prediction matches GT label.
                if age_pred_class == age_lbl:
                    age_correct_count += 1

                gender_pred_class = -1  # Default to unpredicted if GT is invalid
                if gender_lbl != -1:
                    current_gender_logits_no_grad = gender_logits[i].unsqueeze(0)
                    gender_pred_class = torch.argmax(current_gender_logits_no_grad, dim=1).item()
                    if gender_pred_class == gender_lbl:
                        gender_correct_count += 1

                # store true labels and predicted probabilities for confusion Mattrix CM
                true_age_labels_for_cm.append(age_lbl)
                # For cm, it's often better to log probabilities (softmax/sigmoid) or logits
                # than just the hard classification prediction. Let's log softmax probs
                # store [prob_child, prob_adult]
                predicted_age_probs_for_cm.append(F.softmax(current_age_logits.squeeze(), dim=0).cpu().numpy())

                if self.args.debug:
                    age_pred_text = 'adult' if age_pred_class == 1 else 'child'
                    gender_pred_text = 'female' if gender_pred_class == 1 else 'male'
                    logging.debug(f"""{log_prefix} Prediction {i} classification: AgePred: {age_pred_class}
                                  ({age_pred_text}),
                                    GenderPred: {gender_pred_class} ({gender_pred_text})""")

                # increment count of successfully matched and processed detections
                processed_detections_count += 1
        # sum all individual detection loss tensors to get the total loss for this sample
        # if no crops were matched, total loss is 0
        # ensure requires_grad=True for the summed loss tensor if training
        if list_of_loss_tensors:
            total_loss_tensor_for_sample = torch.sum(torch.stack(list_of_loss_tensors))
        else:
            total_loss_tensor_for_sample = torch.tensor(0., device=device, requires_grad=True)

        # Return the total loss tensor for the sample and aggregate counts
        return total_loss_tensor_for_sample, age_correct_count, gender_correct_count, processed_detections_count, true_age_labels_for_cm, predicted_age_probs_for_cm

    def process_dataloader_batch(self, batch_list_of_samples: list,
                                 batch_idx: int, total_batches: int, global_step: int):
        """
        process a btach of data received from the dataloader.
        since our custom collate_fn returns a list of samples, this function iterates through that list,
        calling process_single_sample for each one
        It aggregates the results (loss tensors, correct counts, detection counts) from all samples in the batch

        Args:
            batch_list_of_samples (list): A list where each element is (image_tensor, targer_dict) for a single sample.
            batch_idx (int): Index of the current batch in the DataLoader.
            total_batches (int): Total number of batches in the DataLoader

        Returns:
            tuple: (total_batch_loss_tensor_sum, total_batch_age_correct_sum, total_batch_gender_sum,
            total_detections_processed_in_batch)

             total_batch_age_correct_sum: Total correct age predictions across the batch.
             total_batch_gender_correct_sum: Total correct gender predictions across the batch.
             total_detections_processed_in_batch: Total detections processed in the batch
        """
        log_prefix = f"[atch {batch_idx}/{total_batches}]"
        if self.args.debug:
            logging.debug(f"{log_prefix} Processing DataLoader batch with {len(batch_list_of_samples)} samples")

        total_age_correct_sum = 0  # Accumlator for age correct count across the batch
        total_gender_correct_sum = 0  # Accumlator for gender correct count across the batch
        total_detections_processed_in_batch = 0  # Accumlator for total valid detections processed in the batch

        list_of_sample_loss_tensors = []  # Collect loss tensors from each sample in the batch
        batch_true_age_labels = []
        batch_pred_age_probs = []

        # Iterate through each individual sample (image, target_dict) within batch list
        # unpack original size
        for sample_idx, (img_tensor, target_dict, original_size) in enumerate(batch_list_of_samples):
            # process the single sample using dedicated method
            sample_loss_t, sample_age_correct, sample_gender_correct, \
                processed_detections_count, sample_true_labels_cm, \
                sample_pred_probs_cm = self.process_single_sample(
                    img_tensor,
                    target_dict,
                    sample_idx=sample_idx,
                    global_step=global_step,
                    original_size=original_size
                )

            # if the sample contained any valid detections, add its results to the batch totals
            if processed_detections_count > 0:
                # Append the total loss tensor calculated for this sample
                list_of_sample_loss_tensors.append(sample_loss_t)
                # Accumlate the counts
                total_age_correct_sum += sample_age_correct
                total_gender_correct_sum += sample_gender_correct
                total_detections_processed_in_batch += processed_detections_count

                batch_true_age_labels.extend(sample_true_labels_cm)
                # list of np.array(prob_child, prob_adult)
                batch_pred_age_probs.extend(sample_pred_probs_cm)
            elif self.args.debug:
                logging.debug(f"{log_prefix} Sample {sample_idx}: Processed 0 detections, loss/metrics are zeros")

        # Sum up all the sample-level loss tensors collected for the batch
        # This final tensor represents the total loss across all valid detections in the entire batch
        if list_of_sample_loss_tensors:
            # Stack the list of sample loss tensors into a single tensor, then sum them
            # This ensures the computation graph connects losses from all samples/detections in the batch
            total_batch_loss_tensor_sum = torch.sum(torch.stack(list_of_sample_loss_tensors))
        else:
            # If no detections were processed in the entire batch, total loss is zero
            # create a zero tensor and set require_grads=True. This is important
            # So pytorch doesn't raise an error during the backward pass if no gradients were computed for a batch
            total_batch_loss_tensor_sum = torch.tensor(0., device=self.device, requires_grad=True)

        if self.args.debug:
            logging.debug(f"""{log_prefix} Finished processing DataLoader batch. Total detections processed:
                          {total_detections_processed_in_batch}.
                          Total batch loss tensor sum value: {total_batch_loss_tensor_sum.item():.4f}.""")

        # Log cm/predictions Table here
        logger_instance = getattr(self, 'wandb_logger', None)
        if logger_instance is None and 'wandb_logger' in globals():
            logger_instance = globals()['wandb_logger']

        if (batch_idx % self.args.log_interval == 0 or batch_idx < 3) and logger_instance:
            if batch_true_age_labels and batch_pred_age_probs:
                wandb.log({
                    f"train/batch_{batch_idx}_age_conf_mat": wandb.plot.confusion_matrix(
                        probs=None,  # can be none if preds is provided
                        y_true=batch_true_age_labels,
                        preds=[np.argmax(p) for p in batch_pred_age_probs],  # Convert probs to class
                        class_names=["Child", "Adult"]
                    )
                }, step=global_step)

        # return the aggregated results for the batch
        return (total_batch_loss_tensor_sum, total_age_correct_sum,
                total_gender_correct_sum, total_detections_processed_in_batch,
                batch_true_age_labels, batch_pred_age_probs)


# Handles the creation of datasets and DataLoaders for training and validation
class DataManager:
    def __init__(self, args):
        self.args = args
        # Datasets and DataLoaders are initialized as None and created later
        self.train_dataset = None
        self.val_dataset = None
        self.train_loader = None
        self.val_loader = None

    def create_datasets(self):
        """"
        Creates instances of the custom AgeGenderDataset for the training and validation splits.
        This assumes the dataset class handles loading data based on the provided path and split name ("train", "val"),
        """
        logging.info(f"Creating datasets from {self.args.data_path}")
        # manifest full path
        if os.path.isabs(self.args.manifest_file):
            manifest_full_path = self.args.manifest_file
        else:
            # relative to data path
            manifest_full_path = os.path.join(self.args.data_path, self.args.manifest_file)

        # Determine target_size for transforms (assuming square images for simplicity)
        # Might want to separate input_h, input_w if they can differ
        img_size = getattr(self.args, 'input_size', 224)  # use input size if exists, else default
        if not (isinstance(img_size, int) and img_size > 0):
            logging.warning(f"Invalid input_size {img_size}, defaulting to 224.")
            img_size = 224
        target_image_size = (img_size, img_size)

        try:
            # Instantiate the datast classes
            self.train_dataset = PreCroppedFaceDataset(
                data_root_dir=self.args.data_path,
                manifest_file=manifest_full_path,
                split="train",
                target_size=target_image_size
            )
            self.val_dataset = PreCroppedFaceDataset(
                data_root_dir=self.args.data_path,
                manifest_file=manifest_full_path,
                split="val",
                target_size=target_image_size
            )

            if not self.train_dataset or len(self.train_dataset) == 0:
                logging.critical("Train dataset is Empty or failed to load. Training cannot proceed.")
                raise RuntimeError("Empty training dataset.")  # stop execution

            logging.info(f"Train dataset size: {len(self.train_dataset)}")
            logging.info(f"Val dataset size: {len(self.val_dataset)}")
        except Exception as e:
            # Log an error and re-raise the exception if dataset creation fails
            logging.error(f"error creating datasets from {self.args.data_path}: {e}", exc_info=True)
            raise

    def create_dataloaders(self):
        """
        cretes pytorch DataLoaders for the train and validation datasets.
        DataLoaders handle batching, shuffling, and multiprocessing data loading
        """
        # create the datasets first if they haven't been created already
        if self.train_dataset is None or self.val_dataset is None:
            self.create_datasets()

        # define a custom collate_fn
        # the default collate_fn tries to stack tensors, which works for simple cases
        # However, since our targets are dictionaries and number of detections varies per image
        # it's simpler to just return a list of individual (image, target_dict) tuples for each batch
        # Our processing logic in ModelManager handles iterating over list
        def custom_collate_fn(batch):
            return batch  # This function simply returns the batch as is

        # create the training dataloader
        try:
            self.train_loader = DataLoader(
                self.train_dataset,
                batch_size=self.args.batch_size,
                shuffle=True,
                num_workers=self.args.num_workers,
                collate_fn=custom_collate_fn,
                # Copy tensors to CUDA pinned memory before transferring to GPU
                pin_memory=True if self.args.device.type == "cuda" else False
            )
            logging.info(f"""Train DataLoader created with {len(self.train_loader)}
                         batches of size {self.args.batch_size}""")
            if len(self.train_loader) == 0:
                logging.warning("Train DataLoader is empty. Training may not be possible.")
        except Exception as e:
            logging.error(f"Error creating train DataLoader: {e}")
            self.train_loader = None  # Ensure it is none if creation fails

        # create the validation DataLoader
        try:
            self.val_loader = DataLoader(
                self.val_dataset,
                batch_size=self.args.batch_size,
                shuffle=False,
                num_workers=self.args.num_workers,
                collate_fn=custom_collate_fn,
                pin_memory=True if self.args.device.type == "cuda" else False
            )
            logging.info(f"""Val DataLoader created with {len(self.val_loader)}
                         batches of size {self.args.batch_size}""")
            if len(self.val_loader) == 0:
                logging.warning("Val DataLoader is empty. Validation may not be possible.")
        except Exception as e:
            logging.error(f"Error creating val DataLoader: {e}")
            self.val_loader = None


# Checkpoint Manager Class
# Handles saving and loading training checkpoints (model weights, optimizer state, epoch, step, best metrics)
class CheckpointManager:
    def __init__(self, args):
        self.args = args
        # keep track of the best validation metrics seen so far
        # we initialize age_acc to 0.0 and loss to infinity
        # also track total_detections for handling cases with 0 processed detections.
        self.best_metrics = {"age_acc": 0.0, "age_f1_weighted": 0.0, "loss": float('inf'), "total_detections": 0}

    def save_checkpoint(self, model, optimizer, scheduler, epoch, metrics, global_step, is_best=False):
        """
        Saves the current training state as a checkpoint file.

        Args:
            model nn.Module
            optimizer: The optimizer to save its state.
            scheduler: The learning rate scheduler to save its state. can be None
            epoch
            metrics (dict): Dictionary of metrics from the validation run that triggered this save.
            global_step (int): The total number of batches processed so far.
            is_best (bool)
        """
        # create a checkpoint containing all the information needed to resume training
        checkpoint = {
            "epoch": epoch,  # current epoch number
            "global_step": global_step,
            "model_state_dict": model.state_dict() if hasattr(model, 'model') and isinstance(model.model, nn.Module)
            else model.state_dict(),  # State of the model parameters
            "optimizer_state_dict": optimizer.state_dict(),  # momentums, etc
            # save scheduler only if it exists
            "scheduler_state_dict": scheduler.state_dict() if scheduler else None,
            "metrics": metrics,  # save the validation metrics from this epoch
            "args": vars(self.args),  # command line arguments used for this run
            "best_metrics": self.best_metrics  # tracker for best metrics so far
        }

        os.makedirs(self.args.output_dir, exist_ok=True)
        # define filename for the checkpoint. Padding the epoch makes filenames sortable.
        checkpoint_filename = f"checkpoint_epoch_{epoch:03d}_step{global_step}.pth"
        checkpoint_path = os.path.join(self.args.output_dir, checkpoint_filename)

        # save the checkpoint dictionary to a file
        try:
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"Saved checkpoint to {checkpoint_path}")
        except Exception as e:
            logging.error(f"Error saving best model checkpoint to {checkpoint_path}: {e}")

        # if this checkpoint is the best so far according to out criteria, also save it to a fixed name.
        if is_best:
            best_path = os.path.join(self.args.output_dir, "best_model.pth")
            try:
                # save the same checkpoint dictionary again, overwriting the previous best_model.pth
                # saving a copy is safer/simpler than trying to create symlinks across different systems
                torch.save(checkpoint, best_path)
                # log the validation age accuracy that made this best model.
                logging.info(f"New best model saved to {best_path} with val age acc {metrics.get('age_acc', -1):.4f}")
            except Exception as e:
                logging.error(f"Error saving best model checkpoint to {best_path}: {e}")

    def load_checkpoint(self, checkpoint_path: str,
                        model: nn.Module, optimizer: optim.Optimizer = None):
        """
        loads a checkpoint file to resume training

        Returns:
            the epoch to start from, the global step, the best metrics seen so far,
            and the optimizer/scheduler instances or None if loading failed
        """
        if not os.path.exists(checkpoint_path):
            logging.warning(f"Resume checkpoint file not found at {checkpoint_path}. Starting from scratch.")
            # return initial values indicating start from scratch
            return 0, 0, {"age_acc": 0.0, "loss": float("inf"), "total_detections": 0}, None, None

        logging.info(f"Loading checkpoint for resuming from {checkpoint_path}")
        try:
            # Load checkpoint dictionary. Use map_location to load onto correct device
            checkpoint = torch.load(checkpoint_path, map_location="cuda")
        except FileNotFoundError:
            logging.error(f"Checkpoint file not found at {checkpoint_path} during torch.load. Starting from scratch.", exc_info=True)
        except Exception as e:
            logging.error(f"Error loading resume checkpoint file {checkpoint_path}: {e}. starting from scratch.")
            return 0, 0, {"age_acc": 0.0, "loss": float("inf"), "total_detections": 0}, None, None

        # Load Model state
        if "model_state_dict" not in checkpoint:
            logging.error(f"model_state_dict not found in checkpoint {checkpoint_path}. Cannot resume modeltraining.")
            return 0, 0, {"age_acc": 0.0, "loss": float("inf"), "total_detections": 0}, None, None

        ckpt_model_state_dict = checkpoint["model_state_dict"]

        # Adjust keys: Strip model. prefix if present
        keys_had_prefix = any(k.startswith("model.") for k in ckpt_model_state_dict.keys())

        if keys_had_prefix:
            logging.info("Checkpoint keys have model. prefix. Stripping prefix for loading into nn.Module.")
            stripped_state_dict = {k[len("model."):]: v for k, v in ckpt_model_state_dict.items() if
                                   k.startswith("model.")}
            # Add back any keys that didn't have the prefix.
            for k, v in ckpt_model_state_dict.items():
                if not k.startswith("model."):
                    stripped_state_dict[k] = v
            final_state_dict = stripped_state_dict
        else:  # No model. prefix found. use as is
            final_state_dict = ckpt_model_state_dict
        
        try:
            # Load the adjusted state_dict into nn.Module
            # model is self.model_manager.model.model
            incompatible_keys = model.load_state_dict(final_state_dict, strict=True)
            logging.info(f"Model state dict loaded successfully resume.")
            if incompatible_keys.missing_keys or incompatible_keys.unexpected_keys:
                logging.warning(f"  load_state_dict strict=True reported incompatible keys: {incompatible_keys}")
        except RuntimeError as e:  # Typically raised by load_state_dict on mismatch strict=true
            logging.error(f"Runtime Error loading model state dict from resume checkpoint: {e}", exc_info=True)
            logging.error("Resume failed due to model state mismatch. Starting training from scratch.")
            return 0, 0, {"age_acc": 0.0, "loss": float("inf"), "total_detections": 0}, None, None
        except Exception as e:
            logging.error(f"Error loading model state dict from resume checkpoint: {e}", exc_info=True)
            return 0, 0, {"age_acc": 0.0, "loss": float("inf"), "total_detections": 0}, None, None

        # load optimizer state
        loaded_optimizer = None  # Assume failure until successful load
        # Only attempt to load optimizer state if an optimizer instance was provided And its state exist in checkpoint
        if optimizer and "optimizer_state_dict" in checkpoint and checkpoint["optimizer_state_dict"] is not None:
            try:
                # Load optimizer's state. This restores learning rate, momentum, etc
                # this assumes the set of trainable parameters in the current model matches
                # This set of parameters the optimizer was tracking when the chekpoint was saved.
                # this is why we freeze the backbone constantly and only train the head
                optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
                loaded_optimizer = optimizer  # Loading was successfull
                logging.info("Optimizer state dict loaded successfully for resume.")
            except ValueError as e:  #Common if optimizer params changed
                # If optimizer state loading fails, log as a warning
                # The Optimzer will be re-created based on current settings and loader length
                logging.warning(f"""Optimzer state dict mismatch or loading failed from resume checkpoint: {e}.
                                Optimzer will be re-created from scratch.""")

            # load optimizer state
            scheduler_state_dict_from_ckpt = None  # Assume failure until successful load
            # Only attempt to load scheduler state if an scheduler instance was provided
            # And its state exist in checkpoint
            if "scheduler_state_dict" in checkpoint and checkpoint["scheduler_state_dict"] is not None:
                scheduler_state_dict_from_ckpt = checkpoint["scheduler_state_dict"]
                logging.info("Scheduler state dict loaded successfully for resume.")
            else:
                logging.warning("""Scheduler state dict mismatch or loading failed from resume checkpoint
                                Scheduler will be re-created from scratch.""")

        # Load training progress and Best Metrics
        # get the epoch and global step from checkpoint
        # we want to resume training from the next epoch after one saved
        # Default to -1 if epoch key is missing. so start epoch becomes 0.
        start_epoch = checkpoint.get("epoch", -1) + 1
        # Ensure start epoch is not negative
        if start_epoch < 0:
            start_epoch = 0
            logging.warning(f"Saved epoch was {checkpoint.get('epoch', 'N/A')}. Forcing resume epoch to 0.")

        # get the gloval step from checkpoint, default to 0
        global_step = checkpoint.get("global_step", 0)

        # Get the best validation metrics recorded when checkpoint was saved.
        # Default to initial best_metrics state if key is missing.
        loaded_best_metrics = checkpoint.get("best_metrics", {
            "age_acc": 0.0, "loss": float('inf'), "total_detections": 0})

        logging.info(f"Resuming training from epoch {start_epoch}, global step {global_step}")
        logging.info(f'Loaded best metrics: {loaded_best_metrics}')

        # Return the loaded state information. The transfer will update its internal state with this values
        return start_epoch, global_step, loaded_best_metrics, loaded_optimizer, scheduler_state_dict_from_ckpt


# Trainer Class
# the main class that orchestrates the entire training process.
# It initializes the managers, runs the training and validation loops, and handles saving/loading.
class Trainer:
    def __init__(self, args):
        self.args = args
        self.logger = wandb_logger

        # configure the root logger's level based on the debug command line argument.
        # If debug is True, set level to DEBUG to show all messages, otherwise set to INFO.
        if self.args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            logging.debug("Detailed debugging logs enabled.")
        else:
            logging.getLogger().setLevel(logging.INFO)

        # log the hyper paramteres command line arguments to wandb.
        # self.logger.log_hyperparameters(self.args)

        # Initialize the DataManager and create the DataLoades.
        self.data_manager = DataManager(args)
        self.data_manager.create_dataloaders()  # calls create_datasets internally.

        # initialize modelmanager. Handles model creation, modification, and detector.
        self.model_manager = ModelManager(args)
        # create an optimizer. This must be done after the model parameters are set up (freezing/unfreezing)
        self.model_manager.create_optimizer()

        # Create the scheduler after the DataLoader is created, as it needs the number of batches per epoch.
        # Initialize scheduler as None first
        self.data_manager.scheduler = None
        if self.data_manager.train_loader:
            # Only create the scheduler if the train loader is not empty.
            self.model_manager.create_scheduler(len(self.data_manager.train_loader))
        else:
            logging.warning("Train DataLoader is empty. Scheduler cannot be created.")

        # Initialize the checkpointManager
        self.checkpoint_manager = CheckpointManager(args)

        # Initialize training progress counters.
        self.global_step = 0  # Total number of batches processed across all epochs.
        self.start_epoch = 0  # The epoch number to start training from usually 0, or higher if resuming.
        # Initialize the best validation metrics tracker with the CheckpointManager's default state.
        self.best_val_metrics = self.checkpoint_manager.best_metrics.copy()

        # This ensures self.model_manager.scheduler exists if not resuming.
        # If resuming it might be replaced
        self.model_manager.scheduler = None
        if self.data_manager.train_loader and len(self.data_manager.train_loader) > 0:
            logging.info("Initial creation of scheduler instance will be loaded/replaced if resuming.")
            self.model_manager.create_scheduler(len(self.data_manager.train_loader))
        else:
            logging.warning("Train DataLoader is empty. Scheduler cannot be initially created.")

        # If a resume checkpoint path is provided, load the training state.
        if args.resume:
            logging.info(f"Attempting to resume training from checkpoint: {args.resume}")
            # call the checkpoint manager to load the state.
            # Pass the model, optimizer and scheduler instances so their states can be updated.
            start_epoch_resume, global_step_resume, best_metrics_resume, resumed_optimizer_instance, \
                scheduler_state_dict_from_ckpt = self.checkpoint_manager.load_checkpoint(
                    args.resume,
                    self.model_manager.model.model,  # Pass the nn.Module
                    self.model_manager.optimizer,  # Pass the current optimizer instance

                    # self.model_manager.scheduler
                )
            # Update the trainer's state with the loaded values.
            self.start_epoch = start_epoch_resume
            self.global_step = global_step_resume
            self.best_val_metrics = best_metrics_resume.copy()  # update the best metrics tracker
            self.checkpoint_manager.best_metrics = self.best_val_metrics.copy()  # Sync CM's tracker

            # If optimizer was loaded successfully, update the model manager's instance
            # if not recreate it handled wihin load checkpoint if returning None.
            if resumed_optimizer_instance:
                logging.info("Optimizer state successfully resumed from checkpoint. Using existing optimizer with loaded state.")
                # self.model_manager.optimizer = resumed_optimizer_instance
            else:
                # If loading failed, create a new optimizer based on the current trainable params.
                logging.warning("Optimizer not available after checkpoint load, attempting re-creation.")
                try:
                    self.model_manager.create_optimizer()
                except RuntimeError as e:
                    logging.error(f"Failed to re-create optimizer: {e}. Training cannot proceed.")
                    raise  # Re raise the error if optimizer creation fails

            # Scheduler handling
            if scheduler_state_dict_from_ckpt:  # If state was loaded from checkpoint
                if self.data_manager.train_loader and len(self.data_manager.train_loader) > 0:
                    # Recreate with current config
                    logging.info("Scheduler state dict found. Recreating scheduler and loading state.")
                    self.model_manager.create_scheduler(len(self.data_manager.train_loader))
                    if self.model_manager.scheduler:
                        try:
                            self.model_manager.scheduler.load_state_dict(scheduler_state_dict_from_ckpt)
                            logging.info("Scheduler state loaded successfully into new instance.")

                            # last epoch for onecycle lr is its interl step count -1
                            if hasattr(self.model_manager.scheduler, 'last_epoch'):
                                logging.info("  Scheduler state after load_state_dict: "
                                             f"last epoch = {self.model_manager.scheduler.last_epoch}, "
                                             f"resumed global_step: {self.global_step}")
                                if hasattr(self.model_manager.scheduler, '_step_count'):
                                    logging.info("  Scheduler internal "
                                                 f"_step_count: {self.model_manager.scheduler._step_count}")
                            # Correct OneCycleLR's step to align with global_step from resumed training
                            if isinstance(self.model_manager.scheduler, optim.lr_scheduler.OneCycleLR):
                                self.model_manager.scheduler.last_epoch = self.global_step - 1  # last epoch is step -1
                                logging.info(f"Adjusted OneCycleLR last_epoch to "
                                             f"{self.model_manager.scheduler.last_epoch} based on"
                                             f"resumed global_step {self.global_step}")
                        except Exception as e_sch_load:
                            logging.error(f"Error loading scheduler state dict: {e_sch_load}."
                                          f"Re-creating fresh scheduler.",
                                          exc_info=True)
                    else:
                        logging.error("Failed to create scheduler instance for loading state.")
                else:
                    logging.warning("Train loader empty, cannot apply loaded scheduler state.")
            else:
                logging.warning("No scheduler state in ckpt.")
        
        # Ensure scheduler is None if train_loader is definitively empty now
        if not (self.data_manager.train_loader and len(self.data_manager.train_loader) > 0):
            self.model_manager.scheduler = None
        # Ensure scheduler exists
        if self.model_manager.scheduler is None and self.data_manager.train_loader \
                and len(self.data_manager.train_loader) > 0:
            logging.info("Scheduler was None after resume logic, creating new instance")
            self.model_manager.create_scheduler(len(self.data_manager.train_loader))
        elif not self.data_manager.train_loader or len(self.data_manager.train_loader) == 0:
            logging.warning("Train DataLoader is empty or None. Scheduler can not be created.")
            self.model_manager.scheduler = None

        # Optinal: Configure wandb to watch model prameters and gradients.
        # can add training overhead but is usedul for debuging training dynamics.
        # self.logger.watch_model(self.model_manager.model.model, log="gradients", log_freq=1000)

    def train_epoch(self, epoch):
        """
        Runs one full training epoch.
        Iterates through the training DataLoader, process batches, calculate loss,
        performs backpropagation, updates model weights, and logs training metrics.

        Returns:
            dict: Dictionary containing the average training metrics for the epoch loss, age_acc, gender_acc
        """
        # sets model and its components to training mode.
        self.model_manager.train_mode()

        # Accumlators for metrics over the entire epoch
        # using .item() for scaler values for aggregation.
        total_loss_sum_item = 0.0
        total_age_correct_sum = 0
        total_gender_correct_sum = 0
        total_detections_processed_in_epoch = 0  # Track total detections to calculate correct averages.

        # check if optimizer and train loader are available before starting.
        if self.model_manager.optimizer is None:
            logging.error("Optimizer is None, Cannot perform training step.")
            # Return placeholder values indicating failure.
            return {"loss": -1.0, "age_acc": -1.0, "gender_acc": -1.0, "total_detections": 0}
        if not self.data_manager.train_loader or len(self.data_manager.train_loader) == 0:
            logging.error("Train dataloader is empty, Cannot perform training step.")
            return {"loss": -1.0, "age_acc": -1.0, "gender_acc": -1.0, "total_detections": 0}

        # use tqdm to display a progress bar for the dataloader iteration.
        pbar = tqdm(self.data_manager.train_loader, desc=f"Epoch {epoch} [Train]", leave=False)
        total_batches = len(self.data_manager.train_loader)

        # Iterate through each batch provided by the DataLoader.
        for batch_idx, batch_list_of_samples in enumerate(pbar):
            # batch list of samples is list of image_tenosr, target_dict tuples for this batch,
            # as returned by our custom collate_fn

            # zero out gradients for all trainable parameters before calculating gradients for this batch.
            self.model_manager.optimizer.zero_grad()

            # process the batch. This runs the detector, cropping, model forward pass, and calculates
            # the total loss tensor and counts for all valid detections in this batch.
            batch_loss_tensor_sum, batch_age_correct_sum, \
                batch_gender_correct_sum, batch_detections_count, \
                batch_true_ages, batch_pred_age_probs = self.model_manager.process_dataloader_batch(
                    batch_list_of_samples,
                    batch_idx,
                    total_batches,
                    self.global_step
                )

            # only perform backward pass and optimizer step if there were valid detections processed in the batch.
            # if batch_detections_count is 0, the batch_loss_tensor_sum is a zero tensor with requires_grad=True
            # calling .backward() on it would do nothing,
            #  but it's harmless. We mainly want to skip stepping the optimizer.
            if batch_detections_count > 0:
                if self.args.debug:
                    # log the scalar value of the total batch loss tensor before backward.
                    logging.debug(f"""[Batch {batch_idx}/{total_batches}] Total batch loss tensor value:
                                  {batch_loss_tensor_sum.item():.4f} Performing backward pass.""")

                # Perform backward pass to calculate gradients of the loss with respect to model parameters.
                # Gradients are computed only for parameters where require_grad is True (our head)
                try:
                    batch_loss_tensor_sum.backward()
                except Exception as e:
                    # if backward pass fails
                    logging.error(f"[Batch {batch_idx}/{total_batches}] Error during backward pass: {e}")

                # Apply gradient clipping to prevent exploding gradients, a common technique in training.
                # Clips the gradients of all trainable parameters in model wrapper self.model
                try:
                    torch.nn.utils.clip_grad_norm_(self.model_manager.model.parameters(), max_norm=0.5)
                except Exception as e:
                    logging.error(f"[Batch {batch_idx}/{total_batches}] Error during gradient clipping: {e}")

                # Update the model's parameters using the optimizer based on the calculated gradients.
                # only parameters with requires_grad=True are updated.
                try:
                    self.model_manager.optimizer.step()
                except Exception as e:
                    logging.error(f"[Batch {batch_idx}/{total_batches}] Error during optimizer step: {e}")

                # accumlate the metrics from this batch into epoch totals.
                # use .item() to get scalar value of the loss tensor for aggregation.
                total_loss_sum_item += batch_loss_tensor_sum.item()
                total_age_correct_sum += batch_age_correct_sum
                total_gender_correct_sum += batch_gender_correct_sum
                total_detections_processed_in_epoch += batch_detections_count
            else:
                if self.args.debug:
                    logging.debug(f"""[Batch {batch_idx}/{total_batches}]
                                  No detections processed in batch ({batch_detections_count}).
                                  Skipping backward/optimizer step.""")

            # Increment the global step counter after processing each batch.
            # This is used for scheduling learning rate and logging frequency.
            self.global_step += 1

            # step the learning rate scheduler after optimizer step.
            # onecycleLR typically adjusts the learning rate on per-batch basis.
            # check if scheduler and optimizer exists before stepping.
            if self.model_manager.scheduler and self.model_manager.optimizer:
                try:
                    self.model_manager.scheduler.step()
                except Exception as e:
                    logging.error(f"[Batch {batch_idx}/{total_batches}] Error during scheduler step: {e}")

            # Periodic logging to wandb based on the global step count.
            if (self.global_step % self.args.log_interval) == 0:
                # calculate the current average metrics for epoch based on accumlated values so fat
                # Avoid division by zero if no detections have been processed yet.
                if total_detections_processed_in_epoch > 0:
                    current_avg_loss = total_loss_sum_item / total_detections_processed_in_epoch
                    current_avg_age_acc = total_age_correct_sum / total_detections_processed_in_epoch
                    current_gender_acc = total_gender_correct_sum / total_detections_processed_in_epoch
                else:
                    current_avg_loss = 0.0
                    current_avg_age_acc = 0.0
                    current_gender_acc = 0.0

                # Get the current learning rate from the optimizer's first parameter group.
                current_lr = self.model_manager.optimizer.param_groups[0]["lr"] if self.model_manager.optimizer \
                    else self.args.lr

                # create a dictionary of metrics to log.
                log_metrics = {
                    "train/loss": current_avg_loss,
                    "train/age_acc": current_avg_age_acc,
                    "train/gender_acc": current_gender_acc,
                    "train/lr": current_lr,
                    "epoch": epoch,
                    "batch_idx": batch_idx,  # current batch index within epoch
                    "global_step": self.global_step,
                    # total detections processed this epoch so far
                    # "train/total_dets_in_epoch": total_detections_processed_in_epoch
                }

                self.logger.log_metrics(log_metrics, step=self.global_step)

            # update the tqdm progress bar
            # calculate running averages for display in progress bar
            if total_detections_processed_in_epoch > 0:
                current_avg_loss_pbar = total_loss_sum_item / total_detections_processed_in_epoch
                current_avg_age_acc_pbar = total_age_correct_sum / total_detections_processed_in_epoch
                current_avg_gender_acc_pbar = total_gender_correct_sum / total_detections_processed_in_epoch

                pbar_dict = {
                    "loss": f"{current_avg_loss_pbar: .4f}",
                    "age_acc": f"{current_avg_age_acc_pbar:.2f}",
                    "gender_acc": f"{current_avg_gender_acc_pbar:.2f}"
                }
            else:
                pbar_dict = {
                    "loss": "N/A",
                    "avg_acc": "N/A",
                    "gender_acc": "N/A"
                }

            current_lr_pbar = self.model_manager.optimizer.param_groups[0]['lr'] if self.model_manager.optimizer \
                else self.args.lr
            pbar_dict["lr"] = f"{current_lr_pbar:.2e}"
            pbar_dict["dets"] = total_detections_processed_in_epoch

            pbar.set_postfix(pbar_dict)

        # close the progress bar after the epoch finishes.
        pbar.close()

        # ----- End of Training Epoch calculations ---
        # calculate the final average training metrics for the entire epoch.
        if total_detections_processed_in_epoch > 0:
            epoch_avg_loss = total_loss_sum_item / total_detections_processed_in_epoch
            epoch_avg_age_acc = total_age_correct_sum / total_detections_processed_in_epoch
            epoch_avg_gender_acc = total_gender_correct_sum / total_detections_processed_in_epoch
        else:
            # if no detections were processed in the wholse epoch, average metrics are 0.
            epoch_avg_loss = 0.0
            epoch_avg_age_acc = 0.0
            epoch_avg_gender_acc = 0.0

        # Log the final metrics (appear as dots/lines in wandb at the end of the epoch)
        epoch_log_metrics = {
            "epoch/train_loss": epoch_avg_loss,
            "epoch/train_age_acc": epoch_avg_age_acc,
            "epoch/train_gender_acc": epoch_avg_gender_acc,
            "epoch": epoch
        }
        # log at the final global step reached in this epoch
        self.logger.log_metrics(epoch_log_metrics, step=self.global_step)
        logging.info(f""" ----- End of Epoch {epoch} Train ---- Loss: {epoch_avg_loss:.4f},
                     AgeAcc: {epoch_avg_age_acc:.2f},
                     GenderAcc: {epoch_avg_gender_acc:.2f} ({total_detections_processed_in_epoch} detections)""")

        # return the final epoch metrics
        return {
            "loss": epoch_avg_loss,
            "age_acc": epoch_avg_age_acc,
            "gender_acc": epoch_avg_gender_acc,
            "total_detections": total_detections_processed_in_epoch
        }

    def validate(self, epoch):
        """
        Runs one full validation pass over the validation dataset.
        Iterates through validation Dataloader, process batches, calculate loss and accuracy
        without computing gradients
        """
        # set to eval mode
        self.model_manager.eval_mode()

        # Accumlators for metrics over the entire validation set.
        total_loss_sum_item = 0.0
        total_age_correct_sum = 0
        total_gender_correct_sum = 0
        total_detections_processed_in_val = 0  # Track total detections to calculate correct averages.

        # List to store all labels and predictions for the entire validation set
        all_true_age_labels_epoch = []
        all_pred_age_labels_epoch = []

        if not self.data_manager.val_loader or len(self.data_manager.val_loader) == 0:
            logging.error("Val dataloader is empty, Cannot perform validation.")
            return {"loss": -1.0, "age_acc": -1.0, "gender_acc": -1.0, "total_detections": 0}

        # use torch.no_grad() context manager. This disables gradient computation,
        # which is essential for validation to save memory and speed up the process
        with torch.no_grad():
            pbar = tqdm(self.data_manager.val_loader, desc=f"Epoch {epoch} [Val]", leave=False)
            total_batches = len(self.data_manager.val_loader)

            # iterate through each batch in validation dataloader
            for batch_idx, batch_list_of_samples in enumerate(pbar):
                # process the batch, which runs detector, cropping, model forward pass, and calculate metrics
                # since we are in torch.no_grad(), the loss tensor will not have gradients attached.
                batch_loss_tensor_sum, batch_age_correct_sum, \
                    batch_gender_correct_sum, batch_detections_count, \
                    batch_true_ages, batch_pred_age_probs = self.model_manager.process_dataloader_batch(
                        batch_list_of_samples,
                        batch_idx,
                        total_batches,
                        self.global_step
                    )

                # accumlate metrics
                if batch_detections_count > 0:
                    # add scalar loss value to the accumlator
                    total_loss_sum_item += batch_loss_tensor_sum.item()
                    total_age_correct_sum += batch_age_correct_sum
                    total_gender_correct_sum += batch_gender_correct_sum
                    total_detections_processed_in_val += batch_detections_count

                    # To calculate Precision, Recall, F1 we need all predictions and all true labels
                    # we need to accumlate batch_true_age_labels and batch_pred_age_probs
                    # for the whole validation set.
                    # aggeregate labels and predictions from this batch
                    all_true_age_labels_epoch.extend(batch_true_ages)
                    all_pred_age_labels_epoch.extend(batch_pred_age_probs)  # List of np.arrays

                # Update the tqdm
                if total_detections_processed_in_val > 0:
                    current_avg_loss_pbar = total_loss_sum_item / total_detections_processed_in_val
                    current_avg_age_acc_pbar = total_age_correct_sum / total_detections_processed_in_val
                    current_avg_gender_acc_pbar = total_gender_correct_sum / total_detections_processed_in_val
                    pbar_dict = {
                        "loss": f"{current_avg_loss_pbar: .4f}",
                        "age_acc": f"{current_avg_age_acc_pbar:.2f}",
                        "gender_acc": f"{current_avg_gender_acc_pbar:.2f}"
                    }
                else:
                    pbar_dict = {
                        "loss": "N/A",
                        "avg_acc": "N/A",
                        "gender_acc": "N/A"
                    }
                pbar_dict["dets"] = total_detections_processed_in_val
                pbar.set_postfix(pbar_dict)

            pbar.close()

        # ----- End of Validation Epoch calculations ---
        val_metrics_to_log = {"epoch": epoch}
        return_metrics = {}

        # calculate the final average Validation metrics for the entire epoch.
        if total_detections_processed_in_val > 0:
            val_avg_loss = total_loss_sum_item / total_detections_processed_in_val
            # Simple accuracy (can compare with sklearn's)
            simple_val_age_acc = float(total_age_correct_sum) / total_detections_processed_in_val
            simple_val_gender_acc = float(total_gender_correct_sum) / total_detections_processed_in_val

            val_metrics_to_log["epoch/val_loss"] = val_avg_loss
            val_metrics_to_log["epoch/val_age_acc_manual"] = simple_val_age_acc
            val_metrics_to_log["epoch/val_gender_acc_manual"] = simple_val_gender_acc

            return_metrics["loss"] = val_avg_loss
            return_metrics["age_acc"] = simple_val_age_acc
            return_metrics["total_detections"] = total_detections_processed_in_val

            # calculate detailed metrics using sklearn
            if all_true_age_labels_epoch and all_pred_age_labels_epoch:
                # convert predicted probabilities to class labels
                all_pred_age_labels_epoch = [np.argmax(probs) for probs in all_pred_age_labels_epoch]

                age_accuracy_sk = accuracy_score(all_true_age_labels_epoch, all_pred_age_labels_epoch)

                # average=None gives per class scores. labels=[0, 1] ensures order.
                # precision_per_class, recall_per_class, f1_per_class, support_per_class
                p_age, r_age, f1_age, _ = precision_recall_fscore_support(
                    all_true_age_labels_epoch, all_pred_age_labels_epoch,
                    labels=[0, 1], average=None,
                    zero_division=0
                )
                # For overall scores, 'weighted accounts for class imbalance, macro doesn't
                p_age_w, r_age_w, f1_age_w, _ = precision_recall_fscore_support(
                    all_true_age_labels_epoch, all_pred_age_labels_epoch,
                    average='weighted',
                    zero_division=0
                )
                
                logging.info(f" SKlearn Age Metrics: Accuracy: {age_accuracy_sk:.4f}")
                # Should be for Child 0 and Adult 1
                if len(p_age) == 2:
                    logging.info(f"  Child(0): p: {p_age[0]}, R: {r_age[0]}, F1: {f1_age[0]}")
                    logging.info(f"  Adult(1): p: {p_age[1]}, R: {r_age[1]}, F1: {f1_age[1]}")
                    val_metrics_to_log["epoch/val_age_precision_child"] = p_age[0]
                    val_metrics_to_log["epoch/val_age_recall_child"] = r_age[0]
                    val_metrics_to_log["epoch/val_age_f1_child"] = f1_age[0]
                    val_metrics_to_log["epoch/val_age_precision_adult"] = p_age[1]
                    val_metrics_to_log["epoch/val_age_recall_adult"] = r_age[1]
                    val_metrics_to_log["epoch/val_age_f1_adult"] = f1_age[1]
                
                    logging.info(f" Weighted Avg: P: {p_age_w:.4f}, R: {r_age_w:.4f}, F1: {f1_age_w:.4f}")

                    val_metrics_to_log["epoch/val_age_acc_sklearn"] = age_accuracy_sk
                    val_metrics_to_log["epoch/val_age_precision_weighted"] = p_age_w
                    val_metrics_to_log["epoch/val_age_recall_weighted"] = r_age_w
                    val_metrics_to_log["epoch/val_age_f1_weighted"] = f1_age_w

                    # Update the primary accuracy metric to be used for best model saving
                    return_metrics["age_acc"] = age_accuracy_sk  # Using sklearn's for consistency
                    return_metrics["age_f1_weighted"] = f1_age_w  # F1 is a good one to track for "best"

                    # Log epoch level confusion metrics to wandb
                    try:
                        wandb.log({
                            f"val/epoch_{epoch}_age_conf_mat": wandb.plot.confusion_matrix(
                                probs=None,
                                y_true=all_true_age_labels_epoch,
                                preds=all_pred_age_labels_epoch,
                                class_names=["Child", "Adult"]
                            )
                        }, step=self.global_step)
                    except Exception as e:
                        logging.error(f"Error logging confusion matrix for age classification: {e}")
                else:
                    logging.warning("Validation: No labels/predictions collected for age metrics.")
                    return_metrics["age_acc"] = simple_val_age_acc
                    return_metrics["age_f1_weighted"] = 0.0
        else:
            logging.warning("Validation: No detections processed in this epoch. Default Metrics will be used.")
            return_metrics = {
                "loss": float('inf'),  # No loss if no detections,
                "age_acc": 0.0, "age_f1_weighted": 0.0,
                "total_detections": 0
            }
            val_metrics_to_log["epoch/val_loss"] = float('inf')
            val_metrics_to_log["epoch/val_age_acc_sklearn"] = 0.0
            val_metrics_to_log["epoch/val_age_f1_weighted"] = 0.0

        # log at the final global step reached in this epoch
        self.logger.log_metrics(val_metrics_to_log, step=self.global_step)
        logging.info(f""" ----- End of Epoch {epoch} Validation ----
                     Loss: {return_metrics.get('loss', float('inf')):.4f}, """
                     f"AgeAcc (sk): {return_metrics.get('age_acc', 0.0):.4f}, "
                     f"AgeF1 (w): {return_metrics.get('age_f1_weighted', 0.0):.4f} "
                     # f"GenderAcc: {return_metrics.get('gender_acc', 0.0):.2f} "
                     f"({return_metrics.get('total_detections', 0)} detections)")

        return return_metrics

    def train(self):
        """
        The training loop.
        sets random seeds, runs initial validation, then iterates
        through epochs, running training and validation passes and saving checkpoints.
        """
        logging.info(f"setting random seed: {self.args.seed}")
        random.seed(self.args.seed)
        np.random.seed(self.args.seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(self.args.seed)
            torch.cuda.manual_seed_all(self.args.seed)
            # setting Cudnn deterministic can ensure reproducibility on GPU, but might slow down training slightly.
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False  # often required for determinism

        logging.info(f"Starting training for {self.args.epochs} epochs...")
        # log starting epoch and global step
        logging.info(f"starting from {self.start_epoch}, global step {self.global_step}")

        # Initial validation
        # runs a validation pass before training starts if eval_interval is > 0
        # this gives baseline performance before any training updates
        # if resuming from a checkpoint where initial validation wasn't run it runs now.
        if self.args.eval_interval > 0:
            logging.info("Running Validation....")
            # use a placeholder epoch number for logging the initial run -1 or start_epoch-1
            initial_val_metrics = self.validate(epoch=-1 if self.start_epoch == 0 else self.start_epoch - 1)
            logging.info(f"""[Initial Val] Loss: {initial_val_metrics['loss']:.4f},
                         AgeAcc: {initial_val_metrics['age_acc']:.2f},
                         ({initial_val_metrics['total_detections']} detections)""")

            # Update the best val metrics based on initial values
            if initial_val_metrics["total_detections"] > 0:
                self.best_val_metrics["age_acc"] = initial_val_metrics["age_acc"]
                self.best_val_metrics["loss"] = initial_val_metrics["loss"]
                self.best_val_metrics["total_detections"] = initial_val_metrics["total_detections"]
                logging.info(f"""Best metrics initialized with inital validation results:
                             AgeAcc = {self.best_val_metrics['age_acc']}, Loss: {self.best_val_metrics['loss']:.4f}""")
            else:
                logging.warning("""Initial validation processed 0 detections.
                                Best metrics remain at default acc: 0 and loss: inf""")

            # ensure the checkpoint manger is updated
            self.checkpoint_manager.best_metrics = self.best_val_metrics.copy()

        # ----- main training loop
        # iterates through specified number of epochs. If resuming, starts from start_epoch
        for epoch in range(self.start_epoch, self.args.epochs):
            logging.info(f"\n ----- Epoch {epoch}/{self.args.epochs - 1}------")

            # run the training loop for one epoch
            train_metrics = self.train_epoch(epoch)

            # Log the training metrics
            logging.info(f"Train metrics for Epoch {epoch}: {train_metrics}")
            self.logger.log_metrics(train_metrics, step=self.global_step)

            # validation and checkpoint
            # determine if validation should run this epoch based on eval_interval
            is_validation_epoch = self.args.eval_interval > 0 and (
                (epoch + 1) % self.args.eval_interval == 0 or epoch == self.args.epochs - 1)
            val_metrics = {}

            if is_validation_epoch:
                # run the validation pass
                val_metrics = self.validate(epoch)

                # check if vlaidation is best performance so far
                # Use F1-score as primary metric for the best model selection
                # current_val_age_acc = val_metrics.get("age_acc", -1.0)
                current_val_metrics_for_best = val_metrics.get("age_f1_weighted", 0.0)
                current_val_loss = val_metrics.get("loss", float('inf'))
                current_val_total_dets = val_metrics.get("total_detections", 0)

                is_best = False
                # criteria for best
                # current epoch must have processed more than 0 detections or previous best had 0 detections
                # age accuracy should be higher than previous best age accuracy
                # or age acc is same and loss is lower than prev best loss
                if current_val_total_dets > 0 or self.best_val_metrics["total_detections"] == 0:
                    if current_val_metrics_for_best > self.best_val_metrics.get("age_f1_weighted", 0.0):
                        is_best = True
                        if self.args.debug:
                            logging.debug(f"""New best detected: Age acc improved
                                          ({current_val_metrics_for_best} >
                                          {self.best_val_metrics.get("age_f1_weighted", 0.0)})""")
                    elif current_val_metrics_for_best == self.best_val_metrics.get("age_f1_weighted", 0.0):
                        # if age acc tied compare loss
                        # only compare loss if both curr and prev best processed detections.
                        if current_val_total_dets > 0 and self.best_val_metrics["total_detections"] > 0:
                            if current_val_loss < self.best_val_metrics["loss"]:
                                is_best = True
                                if self.args.debug:
                                    logging.debug(f"""New best detected: Age acc tied,
                                                  Loss improved ({current_val_loss}
                                                  < {self.best_val_metrics['loss']})""")
                        #  if curent has detections but previous has 0 this is better
                        elif current_val_total_dets > 0 and self.best_val_metrics["total_detections"] == 0:
                            is_best = True
                            if self.args.debug:
                                logging.debug("New best detected: Previous best had 0 detections.")

                    else:
                        print("Not a new best")
                else:
                    print("Not a new best")

                # update best if better or tied
                # only on validation that had detections
                if current_val_total_dets > 0:
                    if current_val_metrics_for_best > self.best_val_metrics["age_acc"]:
                        self.best_val_metrics["age_acc"] = current_val_metrics_for_best
                        self.best_val_metrics["loss"] = current_val_loss
                        self.best_val_metrics["total_detections"] = current_val_total_dets

                    elif current_val_metrics_for_best == self.best_val_metrics["age_acc"]:
                        if current_val_loss < self.best_val_metrics["loss"]:
                            self.best_val_metrics["loss"] = current_val_loss
                            self.best_val_metrics["total_detections"] = current_val_total_dets
                    elif current_val_total_dets > 0 and self.best_val_metrics["total_detections"] == 0:
                        self.best_val_metrics["age_acc"] = current_val_metrics_for_best
                        self.best_val_metrics["loss"] = current_val_loss
                        self.best_val_metrics["total_detections"] = current_val_total_dets

                self.checkpoint_manager.best_metrics = self.best_val_metrics.copy()
                if self.args.debug:
                    logging.debug(f"""Current Best Metrics Tracker: Age_acc: {self.best_val_metrics['age_acc']},
                                  Loss:{self.best_val_metrics['loss']}""")

                # determine if checkpoint should be saved this epoch
                # save if defined by save_interval or it's the best model so fat
                is_save_epoch = (epoch + 1) % self.args.save_interval == 0 or is_best

                if is_save_epoch:
                    # Decide which metrics dictionary to save within the checkpoint
                    # if validaton save else best_val
                    # as placeholder, they might be from previous epoch
                    metrics_to_save = val_metrics if is_validation_epoch else self.best_val_metrics

                    self.checkpoint_manager.save_checkpoint(
                        self.model_manager.model,
                        self.model_manager.optimizer,
                        self.model_manager.scheduler,
                        epoch,
                        metrics_to_save,
                        self.global_step,
                        is_best=is_best
                    )

        logging.info("Training Completed")


def main():
    logging.basicConfig(format='%(asctime)s - %(levelname)s - %(message)s', handlers=[logging.StreamHandler()])

    # optinal save log to file
    file_handler = logging.FileHandler('training.log')
    file_handler.setLevel(logging.DEBUG)
    logging.getLogger().addHandler(file_handler)

    args = get_parser().parse_args()

    global wandb_logger
    wandb_logger = WandbLogger(args)

    args.device = torch.device(args.device if torch.cuda.is_available() else "cpu")
    logging.info(f"Using device: {args.device}")

    trainer = Trainer(args)
    trainer.train()


if __name__ == "__main__":
    main()
