import json, os
import logging
from typing import List, Dict, Tuple
from PIL import Image
from torch.utils.data import Dataset


class AgeVerificationDataset(Dataset):
    def __init__(self,
                 manifest_file: str,
                 split: str = "train",
                 task: str = "underage",
                 age_threshold: int = 30,
                 image_base_dir: str = None,
                 transform: Optional[transforms.Compose] = None,
                 age_bins: List[Tuple[int , int]] = None
                 ):
        with open(manifest_file, "r") as f:
            data = json.load(f)
        # annotations.json maybe either a list or a dict with "annotations"
        self.samples = (data if isinstance(data, list) else data.get("annotations", []))
        self.samples = [s for s in self.samples if s.get('split') == split and s.get("image_path")]
        self.task = task
        self.age_threshold = age_threshold
        self.image_base_dir = image_base_dir
        self.age_bins = age_bins

    def __len__(self):
        return len(self.samples)
    
    def _label_from_age(self, age: int) -> int:
        if self.task == "underage":
            return 1 if (age is not None and age < self.age_threshold) else 0
        elif self.task == "age_bins" and self.age_bins:
            for i, (lo, hi) in enumerate(self.age_bins):
                if l