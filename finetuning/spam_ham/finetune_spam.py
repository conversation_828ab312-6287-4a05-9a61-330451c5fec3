import pandas as pd
import torch
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
from transformers import AutoTokenizer, TrainingArguments, Trainer
from transformers.models.gemma3n.modeling_gemma3n import Gemma3nForSequenceClassification
from datasets import Dataset
from transformers import BitsAndBytesConfig

torch.cuda.empty_cache()

MODEL_PATH = "gemma3n_models/unsloth_models/gemma-3n-E2B"
DATA_PATH = "/home/<USER>/Documents/Anas/age_verification_gemma3n/finetuning/data/spam.csv"
OUTPUT_DIR = "/home/<USER>/Documents/Anas/age_verification_gemma3n/finetuning/results"
# Number of output lables 0 for ham 1 for spam
NUM_LABELS = 2

# lOAD AND PREPROCESS DATA
print("Loading and preprocessing data...")
df = pd.read_csv(DATA_PATH, encoding="latin-1")
df = df.rename(columns={'v1': 'label', 'v2': 'text'})
df = df[['label', 'text']]

# Convert labels to numeric values
label_map = {"ham": 0, "spam": 1}
df['label'] = df['label'].map(label_map)

# Split the data into training and validation sets
# stratify=df['label'] ensures that the class distribution is preserved in the split
train_df, val_df = train_test_split(df, test_size=0.2, random_state=42, stratify=df['label'])

# Convert the dataframes to Hugging Face datasets
train_dataset = Dataset.from_pandas(train_df)
val_dataset = Dataset.from_pandas(val_df)

quantization_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4",
)

# Load the tokenizer and model
print("Loading tokenizer and model...")
tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH)
model = Gemma3nForSequenceClassification.from_pretrained(
    MODEL_PATH,
    # quantization_config=quantization_config,
    num_labels=NUM_LABELS
)

# Freeze everything but the last layer
for name, param in model.named_parameters():
    # Keep only classification head trainable.
    # Common names: "score" or "classifier"
    if "score" in name or "classifier" in name:
        param.requires_grad = True
    # unfreeze the last transformer block layer 29
    elif "model.layers.29" in name:
        print(f"Unfreezing {name}")
        param.requires_grad = True
    # Unfreeze the final layer norm
    elif "model.ln_f" in name:
        print(f"Unfreezing {name}")
        param.requires_grad = True
    else:
        param.requires_grad = False

# Reduce activation memory
# model.gradient_checkpointing_enable()

# Ensure tokenizer has a pad token. If missing, reuse eos as pad.
if tokenizer.pad_token is None:
    # Gemma families often don't define a pad token; reusing eos is common
    tokenizer.pad_token = tokenizer.eos_token

# Make the model aware of the pad token id
model.config.pad_token_id = tokenizer.pad_token_id


# Tokenization Function
# This function tokenizes the text data, adding padding and truncation as needed.
def tokenize_function(examples):
    # padding="max_length" pads sequences to the maximum length
    # truncation=True Truncates sequences longer than max_length
    # max_length=128 limits the input length to 128 tokens
    return tokenizer(examples["text"], padding="max_length", truncation=True, max_length=128)


#
# Apply the tokenization function to both training and validation datasets.
print("Tokenizing datasets...")
tokenized_train_dataset = train_dataset.map(tokenize_function, batched=True)
tokenized_val_dataset = val_dataset.map(tokenize_function, batched=True)

# Prepare datasets for PyTorch training:
# Remove the original 'text' column as it's no longer needed after tokenization.
tokenized_train_dataset = tokenized_train_dataset.remove_columns(["text"])
tokenized_val_dataset = tokenized_val_dataset.remove_columns(["text"])
# Rename the 'label' column to 'labels' to match the expected input name for Hugging Face models.
tokenized_train_dataset = tokenized_train_dataset.rename_column("label", "labels")
tokenized_val_dataset = tokenized_val_dataset.rename_column("label", "labels")
# Set the format of the datasets to "torch" tensors.
tokenized_train_dataset.set_format("torch")
tokenized_val_dataset.set_format("torch")

# 5. Define Metrics Calculation
# This function computes evaluation metrics after each evaluation step.


def compute_metrics(eval_pred):
    # eval_pred can be a tuple (preds, labels) or an EvalPrediction with attributes.
    try:
        predictions, labels = eval_pred
    except Exception:
        predictions, labels = eval_pred.predictions, eval_pred.label_ids
    # Convert raw predictions (logits) to class predictions (0 or 1).
    predictions = torch.tensor(predictions)
    if predictions.ndim > 1:
        predictions = torch.argmax(predictions, dim=1)
    # Calculate metrics.
    accuracy = accuracy_score(labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='binary')
    return {"accuracy": accuracy, "precision": precision, "recall": recall, "f1": f1}


# 6. Training Arguments
# Define the training configuration using TrainingArguments.
training_args = TrainingArguments(
    output_dir=OUTPUT_DIR,                 # Directory to save model checkpoints and outputs
    num_train_epochs=2,                    # Total number of training epochs. Adjust based on convergence.
    per_device_train_batch_size=2,        # Batch size per GPU/CPU for training.
    per_device_eval_batch_size=2,         # Batch size per GPU/CPU for evaluation.
    gradient_accumulation_steps=4,        # Number of updates steps to accumulate before performing a backward/update pass.
    fp16=True,                            # Use mixed precision training for faster and more memory-efficient training.
    warmup_steps=500,                      # Number of steps for the learning rate warmup phase.
    weight_decay=0.01,                     # Strength of L2 regularization.
    optim="adamw_torch",              # Use 4-bit AdamW optimizer for memory efficiency.
    logging_dir='./logs',                  # Directory for TensorBoard logs.
    logging_steps=10,                      # Log training metrics every 10 steps.
    eval_strategy="epoch",                # Evaluate the model at the end of each epoch.
    save_strategy="epoch",                 # Save a model checkpoint at the end of each epoch.
    load_best_model_at_end=True,           # After training, load the best model based on `metric_for_best_model`.
    metric_for_best_model="f1",            # The metric to monitor for saving the best model.
    report_to="none",                      # Disable reporting to external services like Weights & Biases.
)

# 7. Initialize and Train Trainer
print("Initializing Trainer...")
# The Trainer class handles the training loop, evaluation, and logging.
trainer = Trainer(
    model=model,                           # The Gemma 3n model to be finetuned.
    args=training_args,                    # The training arguments defined above.
    train_dataset=tokenized_train_dataset,  # The tokenized training dataset.
    eval_dataset=tokenized_val_dataset,    # The tokenized validation dataset.
    processing_class=tokenizer,            # The tokenizer used for preprocessing (new name).
    compute_metrics=compute_metrics,       # The function to compute custom metrics during evaluation.
)

print("Starting training...")
# Start the training process.
trainer.train()

# 8. Save the finetuned model
print("Saving the finetuned model...")
# Save the finetuned model and tokenizer to the output directory.
model.save_pretrained(f"{OUTPUT_DIR}/finetuned_gemma3n_spam")
tokenizer.save_pretrained(f"{OUTPUT_DIR}/finetuned_gemma3n_spam")
