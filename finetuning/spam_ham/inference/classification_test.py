import torch
from transformers import AutoTokenizer
from transformers.models.gemma3n.modeling_gemma3n import Gemma3nForSequenceClassification


# Load the tokenizer and model
print("Loading tokenizer and model...")
tokenizer = AutoTokenizer.from_pretrained("gemma-3n-E4B-it/")
model = Gemma3nForSequenceClassification.from_pretrained("gemma-3n-E4B-it/")

# create a sample input
text = "This is a sample input."
print(f"Sample input: {text}")

# tokenize the input
inputs = tokenizer(text, return_tensors="pt")

# pass the input to the model
print("Performing inference...")
with torch.no_grad():
    outputs = model(**inputs, return_dict=True)

# Analyze the outputs
print("\n---Model Outputs---")
print(outputs)

# check the logits
logits = outputs.logits
print(f"\nLogits shape: {logits.shape}")

# Verify the output
if logits.shape == (1, 2):
    print("\nInference successful!")
else:
    print("\nInference failed. Check the model and input.")
