import sys
sys.path.insert(0, '/home/<USER>/Documents/Anas/age_verification_gemma3n/transformers/src')

from transformers import Gemma3nForConditionalGeneration, Gemma3nProcessor
import torch

ckpt = "/home/<USER>/Documents/Anas/age_verification_gemma3n/gemma-3n-E4B-it"

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = Gemma3nForConditionalGeneration.from_pretrained(ckpt, torch_dtype=torch.bfloat16 if torch.cuda.is_available() else torch.float32).to(device)
processor = Gemma3nProcessor.from_pretrained(ckpt)

prompt = "Explain the theory of relativity in one sentence."
inputs = processor(text=prompt, return_tensors="pt")
inputs = {k: v.to(device) if hasattr(v, "to") else v for k, v in inputs.items()}

with torch.inference_mode():
    out = model.generate(**inputs, max_new_tokens=128, do_sample=True, top_p=0.9, temperature=0.7)

print(processor.decode(out[0], skip_special_tokens=True))
